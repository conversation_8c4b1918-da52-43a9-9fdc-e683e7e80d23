'use client'

import { useState, useEffect } from 'react'
import Link from 'next/link'
import Image from 'next/image'
import { cn } from '@/lib/utils'
import JoinWaitingListCTA from '../../app/(site)/site-components/join-waiting-list-cta'
import { usePathname } from 'next/navigation'

export function Navbar() {
    const [scrolled, setScrolled] = useState(false)
    const currentPath = usePathname()

    // Hide navbar on dashboard route

    // Always declare hooks at the top level
    useEffect(() => {
        const handleScroll = () => {
            setScrolled(window.scrollY > 10)
        }

        window.addEventListener('scroll', handleScroll)
        return () => window.removeEventListener('scroll', handleScroll)
    }, [setScrolled])

    // We now show the navbar on all pages, including auth pages

    return (
        <div className="sticky top-0 left-0 sm:right-0 z-[100] flex justify-center p-4">
            <nav
                className={cn(
                    'flex items-center justify-between w-sm sm:w-xl 3xl:w-2xl pl-3 pr-2 py-1 rounded-lg md:rounded-2xl transition-all duration-300 bg-[#303030]',
                    scrolled ? ' backdrop-blur-md shadow-md border border-[#505050]' : ' backdrop-blur-sm',
                )}>
                {/* Logo with Home Link */}
                <Link href="/" className="flex items-center gap-2 hover:opacity-80 transition-opacity">
                    <Image src="/assets/img/raydian-logo.png" alt="Logo" width={31} height={26} className="h-auto w-auto" />
                    <span className="font-semibold text-sm 3xl:text-[15px] text-[#FAFAFA]">Raydian</span>
                </Link>

                {/* Navigation Links */}
                <div className="flex items-center gap-8">
                    <Link
                        href={currentPath === '/' ? '#pricing' : '/#pricing'}
                        className="hidden md:block text-xs 3xl:text-sm font-normal 3xl:font-medium text-white transition-colors">
                        Pricing
                    </Link>
                    <JoinWaitingListCTA showActive />
                </div>
            </nav>
        </div>
    )
}
