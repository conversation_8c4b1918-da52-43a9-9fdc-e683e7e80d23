'use client'

import React, { useState } from 'react'
import Markdown from 'react-markdown'
import { Copy, ExpandIcon, MinimizeIcon } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { toast } from 'sonner'

interface MarkdownRendererProps {
    content: string
    className?: string
    enableExpansion?: boolean
    initialExpanded?: boolean
    maxPreviewLength?: number
    allowCopy?: boolean
}

export default function MarkdownRenderer({
    content,
    className = '',
    enableExpansion = true,
    initialExpanded = true,
    maxPreviewLength = 200,
    allowCopy = true,
}: MarkdownRendererProps) {
    const [isExpanded, setIsExpanded] = useState(initialExpanded)

    // Handle copying text to clipboard
    const handleCopy = (text: string) => {
        navigator.clipboard.writeText(text)
        toast.success('Copied to clipboard!')
    }

    // Check if content is long enough to warrant expansion controls
    const isLongContent = enableExpansion && content.length > 500
    const displayContent = isLongContent && !isExpanded ? content.substring(0, maxPreviewLength) + '...' : content

    return (
        <div className={className}>
            {/* Action buttons */}
            {allowCopy && (
                <div className="flex flex-row justify-end items-center gap-2">
                    <button className="p-1 rounded hover:bg-gray-200 dark:hover:bg-gray-700" onClick={() => handleCopy(content)}>
                        <Copy size={14} />
                    </button>
                </div>
            )}
            <div className={`markdown-content ${!isExpanded && isLongContent ? 'max-h-32 overflow-hidden relative' : ''}`}>
                <Markdown
                    components={{
                        h1: ({ ...props }) => <h1 className="text-2xl font-bold my-4" {...props} />,
                        h2: ({ ...props }) => <h2 className="text-xl font-semibold my-3" {...props} />,
                        h3: ({ ...props }) => <h3 className="text-lg font-medium my-2" {...props} />,
                        p: ({ ...props }) => <p className="text-[15px] leading-relaxed mb-2" {...props} />,
                        ul: ({ ...props }) => <ul className="list-disc pl-6 mb-2" {...props} />,
                        ol: ({ ...props }) => <ol className="list-decimal pl-6 mb-2" {...props} />,
                        li: ({ ...props }) => <li className="mb-1" {...props} />,
                        a: ({ ...props }) => <a className="text-blue-500 hover:underline" {...props} />,
                        code: ({ ...props }) => (
                            <code
                                className="inline-flex w-fit bg-gray-200 dark:bg-gray-700 p-0 rounded-xl text-xs text-amber-800 my-1 whitespace-pre-wrap"
                                {...props}
                            />
                        ),
                        pre: ({ children, ...props }) => {
                            // Extract plain text from children
                            const codeContent = Array.isArray(children)
                                ? children.map((child) => (typeof child === 'string' ? child : '')).join('')
                                : String(children).trim()

                            return (
                                <div className="relative group ">
                                    <pre
                                        className="bg-gray-200 dark:bg-gray-700 text-xs p-2 rounded my-2 overflow-x-auto"
                                        {...props}>
                                        {children}
                                    </pre>
                                    <button
                                        onClick={() => handleCopy(codeContent)}
                                        className="absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity bg-gray-300 dark:bg-gray-600 p-1 rounded text-xs">
                                        <Copy size={14} />
                                    </button>
                                </div>
                            )
                        },
                        blockquote: ({ ...props }) => (
                            <blockquote className="border-l-4 border-gray-300 pl-4 italic my-2" {...props} />
                        ),
                    }}>
                    {displayContent}
                </Markdown>
            </div>

            {/* Show fade effect and expand button for long content */}
            {isLongContent && (
                <div className={`w-full flex justify-center mt-2 ${!isExpanded ? 'relative' : ''}`}>
                    {!isExpanded && (
                        <div className="absolute bottom-8 left-0 w-full h-12 bg-gradient-to-t from-gray-100 dark:from-gray-800 to-transparent"></div>
                    )}
                    <Button
                        variant="outline"
                        size="sm"
                        onClick={() => setIsExpanded(!isExpanded)}
                        className="text-xs flex items-center gap-1">
                        {isExpanded ? (
                            <>
                                <MinimizeIcon className="h-3 w-3" />
                                Show Less
                            </>
                        ) : (
                            <>
                                <ExpandIcon className="h-3 w-3" />
                                Show More
                            </>
                        )}
                    </Button>
                </div>
            )}
        </div>
    )
}
