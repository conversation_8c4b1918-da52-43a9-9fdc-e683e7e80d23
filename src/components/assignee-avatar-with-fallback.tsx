import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { Tooltip, TooltipContent, TooltipTrigger } from '@/components/ui/tooltip'
import { generateFallbackLetters } from '@/utils/generate-fallback-letters.utils'

interface AssigneeAvatarProps {
    assignee: string
    className?: string
    imageUrl?: string
    fallbackClassName?: string
}

export const AssigneeAvatar: React.FC<AssigneeAvatarProps> = ({
    assignee,
    className = 'h-6 w-6',
    imageUrl = '',
    fallbackClassName = 'text-xs',
}) => {
    return (
        <Tooltip>
            <TooltipTrigger asChild>
                <Avatar className={className}>
                    {imageUrl && <AvatarImage src={imageUrl} alt={assignee} />}
                    <AvatarFallback className={fallbackClassName}>{generateFallbackLetters(assignee)}</AvatarFallback>
                </Avatar>
            </TooltipTrigger>
            <TooltipContent>
                <p>{assignee}</p>
            </TooltipContent>
        </Tooltip>
    )
}
