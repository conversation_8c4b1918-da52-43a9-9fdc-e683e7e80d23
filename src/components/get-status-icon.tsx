import { <PERSON><PERSON><PERSON>ed, <PERSON><PERSON>, Circle<PERSON>heck, AlarmClock } from 'lucide-react'
import Image from 'next/image'
import progressIcon from '../../public/assets/icons/in-progress-icon.svg'

export default function getStatusIcon(label: string, size: number = 15) {
    switch (label.toLowerCase()) {
        case 'backlog':
            return <CircleDashed size={size} />
        case 'in progress':
            return <Image src={progressIcon} width={size} height={size} alt={label} />
        case 'review':
            return <Glasses size={size} />
        case 'completed':
            return <CircleCheck size={size} className="text-[#28A745]" />
        case 'delayed':
            return <AlarmClock size={size} />
        default:
            // No icon for unknown departments
            return null
    }
}
