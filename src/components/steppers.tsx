import { Progress } from './ui/progress'

interface StepperProps {
    currentStep: number // The current active step
    totalSteps: number // The total number of steps
}

const Stepper: React.FC<StepperProps> = ({ currentStep, totalSteps }) => {
    return (
        <div className="flex items-center justify-between w-full mb-4">
            {Array.from({ length: totalSteps }, (_, index) => (
                <div
                    key={index}
                    className={`flex-1 mx-1 rounded h-[5px] overflow-hidden ${
                        currentStep === index
                            ? 'bg-[#08B38B]' // Active step style
                            : 'bg-[#E7FFE4]' // Inactive step style
                    }`}>
                    <Progress value={currentStep === index ? 100 : 0} className="h-full" />
                </div>
            ))}
        </div>
    )
}

export default Stepper
