'use client'

import * as React from 'react'
import { Loader } from 'lucide-react'
import { Label, Legend, Pie, PieChart } from 'recharts'

import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card'
import { ChartConfig, ChartContainer, ChartTooltip, ChartTooltipContent } from '@/components/ui/chart'
import { formatDate } from '@/utils/format-date.utils'

interface ChartDataItem {
    id: number
    label: string
    completed_tasks_count: number
    total_tasks_count: number
    completion_percentage: number | string
    colour: {
        text: string
        border: string
        background: string
    }
    trend_message: string
    calculated_date_from: string
    calculated_date_to: string
    is_top_department: boolean
    top_department_text: string
}

export function TaskDistributionChart({ data, isDataLoading }: { data: ChartDataItem[]; isDataLoading: boolean }) {
    const chartData = React.useMemo(
        () =>
            data?.map((item) => ({
                name: item.label,
                value: item.total_tasks_count || 0,
                fill: item.colour.text,
                ...item,
            })),
        [data], // Only recompute when data changes
    )

    const totalTasks = React.useMemo(() => {
        return chartData?.reduce((acc, curr) => acc + curr.value, 0)
    }, [chartData])

    const chartConfig: ChartConfig = {
        department: {
            label: 'Department',
        },
    }
    const trendMessage = chartData?.length
        ? chartData
              .filter((item) => item.is_top_department)
              .map((item) => item.label + ' ' + item.completion_percentage + '%')
              .join(', ')
        : ''

    if (isDataLoading) {
        return (
            <div className="flex min-h-[400px] items-center justify-center w-full">
                <Loader className="h-6 w-6 animate-spin text-gray-400" />
            </div>
        )
    }

    return (
        <Card className="flex flex-col bg-transparent shadow-none border-0 gap-0">
            <CardHeader className="items-center pb-0">
                <CardTitle>Task Distribution by Department</CardTitle>
                <CardDescription>
                    {chartData?.length > 0 && (
                        <span className="text-sm text-muted-foreground">
                            {formatDate(chartData[0]?.calculated_date_from, 'MM-YYYY')} -{' '}
                            {formatDate(chartData[0]?.calculated_date_to, 'MM-YYYY')}
                        </span>
                    )}
                </CardDescription>
            </CardHeader>
            <CardContent className="flex-1">
                <ChartContainer config={chartConfig} className="mx-auto aspect-square w-[380px] max-h-[270px]">
                    {chartData?.length > 0 ? (
                        <PieChart>
                            <ChartTooltip cursor={false} content={<ChartTooltipContent hideLabel />} />
                            <Pie
                                data={chartData}
                                dataKey="value"
                                nameKey="name"
                                innerRadius={70}
                                strokeWidth={5}
                                isAnimationActive={false}>
                                <Label
                                    content={({ viewBox }) => {
                                        if (viewBox && 'cx' in viewBox && 'cy' in viewBox) {
                                            return (
                                                <text x={viewBox.cx} y={viewBox.cy} textAnchor="middle" dominantBaseline="middle">
                                                    <tspan
                                                        x={viewBox.cx}
                                                        y={viewBox.cy}
                                                        className="fill-foreground text-3xl font-bold">
                                                        {totalTasks.toLocaleString()}
                                                    </tspan>
                                                    <tspan
                                                        x={viewBox.cx}
                                                        y={(viewBox.cy || 0) + 24}
                                                        className="fill-muted-foreground">
                                                        Total Tasks
                                                    </tspan>
                                                </text>
                                            )
                                        }
                                        return null
                                    }}
                                />
                            </Pie>
                            <Legend
                                layout="vertical"
                                verticalAlign="middle"
                                align="right"
                                wrapperStyle={{ paddingTop: '20px' }}
                                formatter={(value: string) => <span className="text-sm text-muted-foreground">{value}</span>}
                            />
                        </PieChart>
                    ) : (
                        <div className="flex h-full items-center justify-center">
                            <p className="text-gray-500">No Tasks available</p>
                        </div>
                    )}
                </ChartContainer>
            </CardContent>
            <CardFooter className="flex-col gap-2 text-sm">
                <div className="flex items-center gap-2 font-medium leading-none">
                    {/* {trendMessage ? 'Highest task completion : ' + trendMessage : 'No tasks were completed in any department.'} */}
                    {trendMessage}
                </div>
                <div className="leading-none text-muted-foreground">Showing total tasks for the last 6 months</div>
            </CardFooter>
        </Card>
    )
}
