export interface Project {
    id: string | number
    name: string
    description?: string
    workspace_id: string | number
    status_id: string | number
    creator: {
        id: string | number
        first_name: string
        last_name: string
    }
    workspace: {
        id: string | number
        name: string
    }
    status: {
        id: string | number
        short_code: string
        label: string
        colour: string
    }
    progress: {
        percentage: number
        completed: number
        total: number
    }
    features: {
        completed: number
        total: number
    }
    team: Array<{
        id: string | number
        first_name: string
        last_name: string
        img_url?: string
    }>
    departments?: Array<{
        id: string | number
        short_code: string
        label: string
        colour: string
        icon: string
    }>
}

export interface Task {
    id: string
    title: string
    priority: string
    description: string
    category: string
    estimatedHours: number
    assignedRole: string
    dependencies: string[]
    acceptanceCriteria: string[]
}

export interface Message {
    id: string
    content: string
    sender: 'user' | 'ai'
    timestamp: Date
}

export interface ProjectData {
    name: string
    description: string
    techStack: Record<string, string>
    tasks: Task[]
}

export interface SSEResponse {
    content?: string
    message?: string
    task?: Task
    error?: string
}

export interface SaveProjectResult {
    success: boolean
    message?: string
}
