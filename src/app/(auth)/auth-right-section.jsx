'use client'
import Image from 'next/image'
import { usePathname } from 'next/navigation'

export default function AuthRightSection() {
    const pathname = usePathname()

    switch (pathname) {
        case '/sign-up':
            return <SignUpRightSection />
        default:
            return <DefaultRightSection />
    }
}

function DefaultRightSection() {
    return (
        <div className="relative w-full h-full ">
            <div className="absolute z-10 top-35 left-20 max-w-[480px]">
                <div className=" text-white text-[22px] font-medium">Think Less. Build Faster.</div>
                <div className="text-sm text-[#C5C5C5] text-justify leading-[24px]">
                    Rely on structured workflows and intelligent timelines. Let <PERSON><PERSON> handle the coordination so you can focus
                    on the vision.{' '}
                </div>
            </div>
            <Image
                src={'/assets/img/signin-right-image.png'}
                fill
                priority
                alt="Sign In Right Section"
                className="object-cover"
                quality={90}
            />
        </div>
    )
}

function SignUpRightSection() {
    return (
        <div className="relative w-full h-full">
            <div className="absolute z-10 top-10 left-14 max-w-[455px]">
                <div className=" text-[#1E293B] text-sm font-medium pb-2">You Dream. We Structure</div>
                <div className="  text-xs text-[#535862] text-justify leading-[24px]">
                    With AI-powered task planning and intelligent workflows, your projects start with clarity, stay on course, and
                    finish strong. No chaos, no guesswork — just structured execution from day one.
                </div>
            </div>

            <Image
                src={'/assets/img/signup-right-image.png'}
                fill
                priority
                alt="Sign Up Right Section"
                className="object-cover"
                quality={90}
            />
        </div>
    )
}
