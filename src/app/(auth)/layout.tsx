import Image from 'next/image'
import Link from 'next/link'
import AuthRightSection from './auth-right-section'

export default function Layout({ children }: { children: React.ReactNode }) {
    return (
        <div className="flex flex-row w-full h-screen overflow-hidden">
            <div className="w-full md:w-[50%] flex justify-center items-center bg-[#fff] overflow-auto custom-scroll relative">
                <Link
                    href="/"
                    className=" flex items-center gap-2 hover:opacity-80 transition-opacity absolute top-20 left-0 md:top-12 sm:top-0 md:left-52 sm:left-20  z-10">
                    <Image src="/assets/img/raydian-logo.png" alt="Logo" width={28} height={28} className="h-auto w-auto" />
                    <span className="font-semibold text-lg">Raydian</span>
                </Link>

                <div>{children}</div>
            </div>
            <div className="hidden md:block w-[50%]">
                <AuthRightSection />
            </div>
        </div>
    )
}
