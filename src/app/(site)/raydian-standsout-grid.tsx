import { Card } from '@/components/ui/card'
import Image from 'next/image'
import CalendarWithTooltip from './site-components/calender'

export default function RaydianStandsOutGrid() {
    return (
        <div className="w-full md:max-w-xl lg:max-w-7xl 3xl:max-w-[1750px] mx-auto lg:mt-20">
            {/* Header */}
            <div className="text-center mb-6 lg:mb-16">
                <h2 className="text-[22px] lg:text-[50px] font-serif font-medium text-[#000000] mb-2">Just Raydian Things</h2>
                <p className="hidden lg:block text-[16px] text-[#000000B2] max-w-lg mx-auto leading-relaxed">
                    A peek into the everyday <b>magic</b> — smart suggestions, seamless planning, and visuals that make sense.
                    This is what it feels like when your project finally runs on flow, not friction.
                </p>
            </div>

            {/* Main Content */}
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 px-2">
                <RoadMapCard />
                <PortraitCard />
                <CalenderCard />
                <CollaborationCard />
                <TaskCard />
                <PeopleCard />
            </div>
        </div>
    )
}

const RoadMapCard = () => {
    return (
        <Card className=" max-h-[699px] lg:max-h-[635px] xl:max-h-[699px]  rounded-[19px] px-4 3xl:px-0 bg-[#FAFAFA] shadow-none">
            <div className="relative h-[350px] 3xl:h-[400px]">
                <Image src={'/assets/img/roadmap.png'} alt="project roadmap" fill className="object-contain w-auto h-auto" />
            </div>
            <div className="pl-6 3xl:pl-12 lg:mt-18">
                <p className="font-bold text-[32px] text-[#141414] font-serif">Roadmap</p>
                <p className=" text-[18px] text-[#141414]">Where your project&apos;s future meets its present, side by side.</p>
            </div>
        </Card>
    )
}

const PortraitCard = () => {
    return (
        <div className="h-[699px] md:h-[759px] lg:h-[680px] xl:h-[759px]  p-0 rounded-[19px] overflow-hidden relative">
            <Image src={'/assets/img/men-portrait.png'} alt="man portrait" fill className="object-cover w-auto h-auto" />
            <div className="absolute bottom-6 left-6 text-white">
                <h3 className="text-2xl font-semibold">Forget The Guesswork</h3>
                <p className="text-sm">Precise, AI-powered estimates - so you can plan with confidence and deliver on time.</p>
            </div>
        </div>
    )
}

const CalenderCard = () => {
    return (
        <Card className="h-fit lg:h-fit xl:h-[635px] rounded-[19px] bg-[#FAFAFA] shadow-none">
            <CalendarWithTooltip />
            <div className="pl-6 3xl:pl-12 lg:mt-2">
                <p className="font-bold text-[32px] text-[#141414] font-serif">Time, Well Spent</p>
                <p className=" text-[18px] text-[#141414]  pr-4">
                    Your entire project, neatly laid out — no missed dates, no surprises.
                </p>
            </div>
        </Card>
    )
}

const CollaborationCard = () => {
    return (
        <Card className="h-fit lg:h-[639px] xl:h-[699px] lg:mt-[-9%] 3xl:mt-[-7%] bg-[#FAFAFA] shadow-none">
            <div className="relative h-[400px]">
                <Image
                    src={'/assets/img/collab.png'}
                    alt="collaboration"
                    fill
                    className="object-contain w-auto h-auto lg:px-2 xl:px-0"
                />
            </div>
            <div className="pl-6 3xl:pl-12 lg:mt-16">
                <p className="font-bold text-[32px] text-[#141414] font-serif">Easy collaboration</p>
                <p className=" text-[18px] text-[#141414]  pr-4">
                    Seamlessly collaborate with your team members like never before
                </p>
            </div>
        </Card>
    )
}

const TaskCard = () => {
    return (
        <Card className="h-fit  lg:h-[612px] xl:h-[664px] bg-[#D5F0E3] shadow-none">
            <div className="pl-6 3xl:pl-12 lg:mt-16">
                <p className="font-bold text-[32px] text-[#141414] font-serif mb-4">Clarity Before Code</p>
                <p className=" text-[16px] text-[#141414]  pr-4">
                    Turn vague inputs into clear, structured user stories — so your team knows exactly what to build, before they
                    start.
                </p>
            </div>
            <div className="relative h-[400px]">
                <Image
                    src={'/assets/img/generated-tasks.png'}
                    alt="ai generated tasks"
                    fill
                    className="object-contain w-auto h-auto pl-4 md:pl-8"
                />
            </div>
        </Card>
    )
}

const PeopleCard = () => {
    return (
        <div className="h-[699px] lg:h-[689px] xl:h-[758px] lg:mt-[-23%] 3xl:mt-[-16%] shadow-none relative rounded-[19px] overflow-hidden">
            <Image src={'/assets/img/peoples.png'} alt="Peoples" fill className="object-cover w-auto h-auto" />
            <div className="absolute bottom-15 3xl:bottom-30 left-6 text-white">
                <p className="font-bold text-[30px] 3xl:text-[32px] text-[#FFFFFF] font-serif mb-4">
                    Your Vision Isn&apos;t Conventional. Your Workflow Shouldn&apos;t Be.
                </p>
                <p className=" text-[16px] text-[#FFFFFF]  pr-4">Move with purpose, not just process.</p>
            </div>
        </div>
    )
}
