import { Accordion, AccordionTrigger, AccordionContent } from '@/components/ui/accordion'
import { AccordionItem } from '@radix-ui/react-accordion'
import { CheckCircle, Users, FileChartColumn, Map, FolderKanban } from 'lucide-react'
import Image from 'next/image'

export default function RaydianStandsOutKanban() {
    const features = [
        {
            icon: CheckCircle,
            title: 'Project Management',
            description:
                'Assign, prioritize, and update tasks without friction. <PERSON><PERSON> keeps everyone on the same page with clear status updates and smart suggestions.',
        },
        {
            icon: FileChartColumn,
            title: 'AI Insights',
            description:
                'Let Raydian surface trends, roadblocks, and team performance patterns. It learns from your projects and offers suggestions that help improve delivery.',
        },
        {
            icon: Map,
            title: 'Roadmap',
            description:
                '<PERSON><PERSON> generates a smart project roadmap using AI, then tracks actual progress alongside it. A built-in deviation metric shows how far you are from the plan, so you can catch delays early.',
        },
        {
            icon: Users,
            title: 'User Management',
            description:
                'Easily onboard teammates, define roles, and manage permissions. <PERSON><PERSON> ensures the right people have the right access, without complexity.',
        },
        {
            icon: Folder<PERSON><PERSON><PERSON>,
            title: 'Kanban Board',
            description:
                'A clean, customizable Kanban view keeps work flowing. Drag, drop, and manage tasks in real-time to guide your next step',
        },
    ]

    return (
        <div className="w-full md:max-w-xl lg:max-w-7xl 3xl:max-w-[80%] mx-auto my-8 md:my-14 lg:my-0 lg:mt-10">
            {/* Header */}
            <div className="text-center  lg:mb-16">
                <h2 className="text-[22px] lg:text-[50px] font-serif font-medium text-[#000000] mb-2">How We Stand Out</h2>
                <p className="hidden lg:block text-[16px] text-[#000000B2] max-w-lg mx-auto leading-relaxed">
                    Raydian brings intelligence, structure, and momentum to your workflow. It auto-generates tasks, offers
                    accurate time and effort estimates, and keeps you posted.
                </p>
            </div>

            {/* Main Content */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-0 lg:gap-2 items-start md:mx-10 lg:mx-4">
                {/* Features List */}
                <div className="space-y-2 xl:space-y-8 3xl:space-y-16 hidden lg:block">
                    {features.map((feature, index) => {
                        const IconComponent = feature.icon
                        return (
                            <div key={index} className="flex gap-4">
                                <div className="flex-shrink-0">
                                    <div className="w-12 h-12 bg-[#08B38B05] border border-[#E9EAEB]  rounded-lg flex items-center justify-center">
                                        <IconComponent className="w-6 h-6 text-teal-600" />
                                    </div>
                                </div>
                                <div className="flex-1">
                                    <h3 className="text-xl font-medium text-gray-900 mb-1 font-serif">{feature.title}</h3>
                                    <p className="text-[#0D0D0DB2] lg:leading-[20px] xl:leading-[24px]  text-sm xl:text-[16px] xl:w-[483px] text-justify ">
                                        {feature.description}
                                    </p>
                                </div>
                            </div>
                        )
                    })}
                </div>
                <div className="w-full">
                    {/* Kanban Board Preview */}
                    <Image
                        src={'/assets/img/kanban-preview.png'}
                        width={1000}
                        height={900}
                        alt="Kanban Board Preview"
                        className="w-auto h-auto "
                    />
                    <Accordion type="single" collapsible className="w-full space-y-8 block md:mt-4 lg:hidden px-4">
                        {features.map((feature, index) => {
                            const IconComponent = feature.icon
                            return (
                                <AccordionItem value={`item-${index}`} key={index} className="border-b">
                                    <AccordionTrigger>
                                        <div className="flex gap-2 items-center">
                                            <div className="w-12 h-12 bg-[#08B38B05] border border-[#E9EAEB]  rounded-lg flex items-center justify-center">
                                                <IconComponent className="w-6 h-6 text-teal-600" />
                                            </div>
                                            <h3 className="text-[18px] lg:text-xl font-medium text-gray-900 mb-1 font-serif">
                                                {feature.title}
                                            </h3>
                                        </div>
                                    </AccordionTrigger>
                                    <AccordionContent className="flex-1">
                                        <p className="text-[#0D0D0DB2] leading-[24px] text-sm lg:text-[16px] w-full text-justify ">
                                            {feature.description}
                                        </p>
                                    </AccordionContent>
                                </AccordionItem>
                            )
                        })}
                    </Accordion>
                </div>
            </div>
        </div>
    )
}
