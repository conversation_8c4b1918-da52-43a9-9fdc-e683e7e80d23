import React from 'react'
import Hero from './hero'
import TaskAndInsights from './task-insights'
import RaydianStandsOutKanban from './raydian-standsout-kanban'
import RaydianStandsOutGrid from './raydian-standsout-grid'
import CustomerCarousel from './customer-carousels'
import PricingSection from './pricing'
import Faq from './faq'
import Join<PERSON><PERSON><PERSON><PERSON><PERSON> from './join-waiting-list'

export default function HomePage() {
    return (
        <div className="overflow-hidden">
            <section id="hero">
                <Hero />
            </section>
            <section id="task-insights">
                <TaskAndInsights />
            </section>
            <section id="raydian-standsout">
                <RaydianStandsOutKanban />
            </section>
            <section id="raydian-standsout-grid">
                <RaydianStandsOutGrid />
            </section>
            <section id="customer-carousels">
                <CustomerCarousel />
            </section>
            <section id="pricing">
                <PricingSection />
            </section>
            <section id="faq">
                <Faq />
            </section>
            <section id="join-waiting-list">
                <JoinWaitingList />
            </section>
        </div>
    )
}
