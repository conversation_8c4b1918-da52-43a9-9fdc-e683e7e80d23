'use client'

import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> } from 'lucide-react'
import { <PERSON>, CardContent } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import Image from 'next/image'

export default function TaskAndInsights() {
    return (
        <div className=" bg-gray-50 lg:py-12 px-4 sm:px-6 lg:px-8 -mt-20 xl:-mt-40 ">
            <div className="max-w-7xl 3xl:max-w-[80%] mx-auto">
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-16 xl:gap-38">
                    {/* Task Generation Section */}
                    <div className="space-y-8">
                        <div className="flex flex-col md:items-center lg:items-start space-y-4 text-center lg:text-left">
                            <h2 className="text-22px md:text-3xl lg:text-[50px] font-medium font-serif text-gray-900 leading-tight">
                                Task Generation
                            </h2>
                            <p className="text-gray-600 text-[14px] md:text-[15px] lg:text-[16px] leading-relaxed md:max-w-lg ">
                                Turn raw inputs or briefs into actionable tasks instantly. Raydian&apos;s AI understands context
                                and auto-generates detailed user stories that match your team&apos;s workflow and tech stack.
                            </p>
                        </div>

                        <div className="space-y-2 bg-white border border-gray-200 md:mx-10 lg:mx-0 pt-6 px-5 lg:px-8 max-h-[472px] rounded-2xl">
                            <div>
                                <h3 className="text-sm lg:text-xl font-semibold text-gray-900 mb-2">AI conversations</h3>
                                <p className="text-gray-500 text-xs md:text-[15px] lg:text-sm w-full md:w-[55%] lg:w-full xl:w-[55%]">
                                    Users chat with AI as they answer your questions, providing an awesome personalized
                                    experience.
                                </p>
                            </div>

                            <Card className="bg-transparent border-none shadow-none">
                                <CardContent className="px-0 space-y-8">
                                    {/* User Message */}
                                    <div className="flex justify-start w-full">
                                        <div className="w-[80%] lg:w-[90%] xl:w-[80%] 3xl:w-[60%] bg-linear-to-r from-[#61B58D] to-[#E3E7A1] rounded-lg p-4 border relative ">
                                            <div className="absolute -top-4 left-2 p-2 bg-white rounded-full">
                                                <Image
                                                    src={'/assets/img/raydian-logo.png'}
                                                    width={20}
                                                    height={20}
                                                    alt="Scratchpad bot"
                                                    className="h-auto w-[20px]"
                                                />
                                            </div>
                                            <p className="text-[#6B7280] text-xs xl:text-sm">
                                                Hi there! © How can I assist you with your project idea today? What are you
                                                thinking about building?
                                            </p>
                                            <div className="flex items-center gap-2 mt-3">
                                                <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                                                    <RotateCcw className="h-4 w-4" color="#666F8D" />
                                                </Button>
                                                <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                                                    <Copy className="h-4 w-4" color="#666F8D" />
                                                </Button>
                                                <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                                                    <MoreHorizontal className="h-4 w-4" color="#666F8D" />
                                                </Button>
                                            </div>
                                        </div>
                                    </div>
                                    {/* AI Response */}
                                    <div className="flex justify-end w-full">
                                        <div className="w-[80%] lg:w-[90%] xl:w-[80%] 3xl:w-[60%] flex gap-3 border rounded-lg p-4 relative">
                                            <div className="absolute -top-2 left-2 rounded-full">
                                                <Image
                                                    src={'/assets/img/user-chat.png'}
                                                    width={25}
                                                    height={25}
                                                    alt="Scratchpad bot"
                                                    className="h-auto w-[25px]"
                                                />
                                            </div>
                                            <div className="flex-1">
                                                <p className="text-gray-700 text-xs xl:text-sm mb-3">
                                                    Hi there! © How can I assist you with your project idea today? What are you
                                                    thinking about building?
                                                </p>
                                                <div className="flex items-center gap-2">
                                                    <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                                                        <RotateCcw className="h-4 w-4" color="#666F8D" />
                                                    </Button>
                                                    <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                                                        <Copy className="h-4 w-4" color="#666F8D" />
                                                    </Button>
                                                    <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                                                        <MoreHorizontal className="h-4 w-4" color="#666F8D" />
                                                    </Button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </CardContent>
                            </Card>
                        </div>
                    </div>

                    {/* Contextual Insights Section */}
                    <div className="space-y-8 ">
                        <div className="flex flex-col md:items-center lg:items-start space-y-4 text-center lg:text-left">
                            <h2 className="text-22px  md:text-3xl lg:text-[50px] font-serif font-medium text-gray-900 leading-tight">
                                Contextual Insights
                            </h2>
                            <p className="text-gray-600 text-[14px] lg:text-[16px] leading-relaxed max-w-lg">
                                Raydian tracks progress in real time, surfacing insights on team performance and work patterns. It
                                flags blockers and delays, so you can fix what&apos;s slowing things down, fast.
                            </p>
                        </div>

                        <div className="space-y-2 bg-white border border-gray-200 pt-6 lg:pb-4 xl:pb-0 px-5 lg:px-8 rounded-2xl md:mx-10 lg:mx-0 max-h-[472px] overflow-hidden">
                            <div>
                                <h3 className="text-sm lg:text-xl font-semibold text-gray-900 mb-2">Mapped Overview</h3>
                                <p className="text-gray-500 text-xs md:text-[15px] lg:text-sm w-full md:w-[55%] lg:w-full xl:w-[55%]">
                                    Raydian turns quantifiable queries like timelines, workloads, and performance data into clear,
                                    actionable visuals.
                                </p>
                            </div>

                            <Card className="bg-transparent border-none shadow-none ">
                                <CardContent className="pb-0 px-0 lg:px-4 ">
                                    {/* Placeholder Image */}
                                    <div className="mt-8 md:mt-0 lg:mt-8 3xl:mt-0 mb-0 3xl:mb-6">
                                        <Image
                                            src={'/assets/img/insights-graph.png'}
                                            width={1000}
                                            height={300}
                                            alt="Analytics Chart Placeholder"
                                            className="w-full h-auto"
                                        />
                                    </div>

                                    {/* Chart description */}
                                    <div className="space-y-1 pt-10 3xl:pt-0">
                                        <div className="flex items-center gap-2">
                                            <span className="text-sm font-medium text-gray-900">
                                                Trending up by 5.2% this month
                                            </span>
                                            <TrendingUp className="h-4 w-4 text-green-500" />
                                        </div>
                                        <p className="text-sm text-gray-500">Showing task completion for the last 6 months</p>
                                    </div>
                                </CardContent>
                            </Card>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    )
}
