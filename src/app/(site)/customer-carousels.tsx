'use client'

import { Card, CardContent } from '@/components/ui/card'
import {
    Carousel,
    CarouselContent,
    CarouselItem,
    CarouselNext,
    CarouselPrevious,
    type CarouselApi,
} from '@/components/ui/carousel'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { useEffect, useState } from 'react'

const testimonials = [
    {
        id: 1,
        name: '<PERSON>',
        title: 'Digital Director, Office of Research',
        content:
            'Implementing TechStack has been a 10/10 experience. It definitely made it easy for our marketing team to get up and running with a new website in no time.',
        avatar: '',
        initials: 'A<PERSON>',
    },
    {
        id: 2,
        name: '<PERSON><PERSON><PERSON>',
        title: 'CEO and Co-Founder',
        content:
            'Integrating TechStack has been a game changer. What used to be time-consuming tasks are now automated, allowing us to focus on strategic growth.',
        avatar: '',
        initials: 'GG',
    },
    {
        id: 3,
        name: '<PERSON>',
        title: 'Automotive Buyer',
        content:
            "I love the TechStack AI setup and can't wait to see what new developments they have in store. The platform has transformed our workflow completely.",
        avatar: '',
        initials: 'D<PERSON>',
    },
    {
        id: 4,
        name: '<PERSON>',
        title: 'Chief Product Officer, <PERSON>-<PERSON>',
        content:
            "TechStack is exactly what we need as a small business platform team. It's flexible, intuitive, and scales with our growing needs.",
        avatar: '',
        initials: 'MC',
    },
    {
        id: 5,
        name: 'Sarah Johnson',
        title: 'Marketing Manager, StartupCo',
        content:
            'The integration process was seamless and the results have been outstanding. Our team productivity has increased by 40% since implementation.',
        avatar: '',
        initials: 'SJ',
    },
]

export default function TestimonialsCarousel() {
    const [api, setApi] = useState<CarouselApi>()
    const [canScrollNext, setCanScrollNext] = useState(false)
    const [canScrollPrev, setCanScrollPrev] = useState(false)

    useEffect(() => {
        if (!api) {
            return
        }

        // Check if we can scroll next/previous
        setCanScrollNext(api.canScrollNext())
        setCanScrollPrev(api.canScrollPrev())

        api.on('select', () => {
            setCanScrollNext(api.canScrollNext())
            setCanScrollPrev(api.canScrollPrev())
        })
    }, [api])

    return (
        <div className="w-full max-w-7xl 3xl:max-w-[1750px] mx-auto px-4 py-6 lg:py-12 ">
            <div className="relative">
                <Carousel
                    setApi={setApi}
                    className="w-full"
                    opts={{
                        align: 'start',
                    }}>
                    <div className="flex justify-between items-start lg:mt-8">
                        <div className="flex sm:justify-center lg:justify-end w-full lg:w-[70%] 3xl:w-2/3 ">
                            <div className="text-center lg:mb-12">
                                <h2 className="text-[22px] lg:text-[50px] font-semibold lg:font-medium text-gray-900 mb-4 font-serif px-14 lg:px-0 w-sm lg:w-xl">
                                    See what our customers had to say.
                                </h2>
                            </div>
                        </div>
                        <div className="hidden lg:flex space-x-2  w-[30%] 3xl:w-1/3 justify-end pt-8">
                            <CarouselPrevious
                                className={`relative inset-0 translate-y-0 h-[38px] w-[38px] bg-black text-white ${
                                    !canScrollPrev ? 'opacity-50 cursor-not-allowed bg-white text-black' : ''
                                }`}
                                disabled={!canScrollPrev}
                            />
                            <CarouselNext
                                className={`relative inset-0 translate-y-0 h-[38px] w-[38px] bg-black text-white ${
                                    !canScrollNext ? 'opacity-50 cursor-not-allowed bg-white text-black' : ''
                                }`}
                                disabled={!canScrollNext}
                            />
                        </div>
                    </div>
                    <CarouselContent className="-ml-2 lg:-ml-4 mb-2">
                        {testimonials.map((testimonial) => (
                            <CarouselItem key={testimonial.id} className="pl-3 lg:pl-4 md:basis-1/2  lg:basis-1/3 3xl:basis-1/4">
                                <CarouselCard testimonial={testimonial} />
                            </CarouselItem>
                        ))}
                    </CarouselContent>
                    <div className="flex lg:hidden space-x-2  w-full justify-center pt-8">
                        <CarouselPrevious
                            className={`relative inset-0 translate-y-0 h-[38px] w-[38px] bg-black text-white ${
                                !canScrollPrev ? 'opacity-50 cursor-not-allowed bg-white text-black' : ''
                            }`}
                            disabled={!canScrollPrev}
                        />
                        <CarouselNext
                            className={`relative inset-0 translate-y-0 h-[38px] w-[38px] bg-black text-white ${
                                !canScrollNext ? 'opacity-50 cursor-not-allowed bg-white text-black' : ''
                            }`}
                            disabled={!canScrollNext}
                        />
                    </div>
                </Carousel>
            </div>
        </div>
    )
}

const CarouselCard = ({ testimonial }: { testimonial: (typeof testimonials)[0] }) => {
    return (
        <Card className="h-full border shadow-xs">
            <CardContent className="px-6 flex flex-col h-full gap-2">
                <div className="flex items-center mb-4">
                    <Avatar className="h-12 w-12 mr-4">
                        {testimonial.avatar && <AvatarImage src={testimonial.avatar} alt={testimonial.name} />}
                        <AvatarFallback className="bg-gray-200 text-gray-600 font-medium">{testimonial.initials}</AvatarFallback>
                    </Avatar>
                </div>
                <blockquote className="text-sm text-gray-700 leading-relaxed flex-grow">
                    &quot;{testimonial.content}&quot;
                </blockquote>
                <div>
                    <h3 className="font-semibold text-gray-900 text-sm">{testimonial.name}</h3>
                    <p className="text-xs text-gray-600">{testimonial.title}</p>
                </div>
            </CardContent>
        </Card>
    )
}
