import Link from 'next/link'
import { Card } from '../../../components/ui/card'
import Image from 'next/image'
import <PERSON><PERSON><PERSON><PERSON>ingListCTA from './join-waiting-list-cta'
// import { Instagram, Linkedin, Youtube } from "lucide-react"
import Instagram from '../../../../public/assets/icons/insta.svg'
import Youtube from '../../../../public/assets/icons/youtube.svg'
import Linkedin from '../../../../public/assets/icons/linkedin.svg'

const SiteFooter = () => {
    return (
        <footer className="mx-0 md:mx-1 lg:mx-7 overflow-hidden">
            <Card className="bg-[#303030] pl-8 pr-8 pt-8 pb-8 lg:pl-15 lg:pr-4 rounded-none md:rounded-[20px] border-none ">
                <div className="block lg:flex justify-between items-center ">
                    <Link href="/" className="flex items-center gap-2 hover:opacity-80 transition-opacity">
                        <Image src="/assets/img/raydian-logo.png" alt="Logo" width={31} height={26} className="h-auto w-auto" />
                        <span className="font-semibold text-sm 3xl:text-[15px] text-[#FAFAFA]">Raydian</span>
                    </Link>
                    <div className="mt-4 lg:mt-0 w-sm lg:w-xs 3xl:w-xl lg:pr-8 3xl:pr-16 font-serif text-white text-[20px] lg:text-sm 3xl:text-[25px] font-normal 3xl:font-medium transition-colors">
                        Be among the first to experience AI-powered task management.
                    </div>
                </div>

                <div className="flex flex-col lg:flex lg:flex-row justify-between lg:items-center mb-2 3xl:mb-4">
                    <div className="flex flex-col gap-4 order-2 lg:order-1 mt-16 lg:mt-0">
                        <p className="text-xs 3 text-white opacity-40">Contact Us</p>
                        <p className="text-sm  text-white opacity-80"><EMAIL></p>
                    </div>
                    <div className="order-1 lg:order-2 lg:w-fit 3xl:w-xl text-white text-sm 3xl:text-[25px] font-normal 3xl:font-medium transition-colors">
                        <JoinWaitingListCTA showIcon={false} buttonClassName="h-[40px] px-6 md:px-29 lg:px-6" />
                    </div>
                </div>
                <div className="space-y-4">
                    <p className="text-xs text-white opacity-40">Follow us</p>
                    <div className="flex gap-2">
                        <div className="p-2 rounded-full border border-gray-500">
                            <Image src={Instagram} width={22} height={22} alt="Instagram" />
                        </div>
                        <div className="p-2 rounded-full border border-gray-500 flex items-center">
                            <Image src={Youtube} width={22} height={22} alt="Youtube" />
                        </div>
                        <div className="p-2 rounded-full border border-gray-500">
                            <Image src={Linkedin} width={22} height={22} alt="Linkedin" />
                        </div>
                    </div>
                </div>

                <div className="hidden lg:flex gap-2 3xl:gap-4 opacity-70 mt-6 3xl:mt-8">
                    <p className="text-xs text-white ">© 2025 Raydian,Inc</p>
                    <p className="text-xs text-white ml-6 3xl:ml-10 ">Terms and Conditions</p>
                    <p className="text-xs text-white ml-2 ">Privacy Policy</p>
                </div>

                <div className="block lg:hidden opacity-70 mt-2 space-y-4 ">
                    <p className="text-xs text-white ">Privacy Policy</p>
                    <p className="text-xs text-white ">Terms and Conditions</p>
                    <p className="text-xs text-white ">© 2025 Raydian,Inc</p>
                </div>
            </Card>
        </footer>
    )
}

export default SiteFooter
