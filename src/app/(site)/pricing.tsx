import { Check, User, Building2, DollarSign, Zap, X } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'

const pricingPlans = [
    {
        id: 'personal',
        name: 'Personal',
        icon: User,
        target: 'For individuals',
        price: 30,
        description: 'Starter-level package for starting businesses',
        popular: false,
        features: [
            { name: 'Centralized Workspace', included: true },
            { name: 'Task Management', included: true },
            { name: 'Secure File Sharing (up to 5GB)', included: true },
            { name: 'Built-In Chat', included: true },
            { name: 'Analytics Dashboard', included: false },
            { name: 'Real-Time Collaboration', included: false },
            { name: 'Seamless Integrations', included: false },
            { name: 'Priority Support', included: false },
        ],
    },
    {
        id: 'pro',
        name: 'Pro',
        bg: 'bg-[#0000000F]',
        icon: Zap,
        target: 'For Startups',
        price: 90,
        description: 'Advanced package for growing businesses',
        popular: true,
        features: [
            { name: 'Centralized Workspace', included: true },
            { name: 'Task Management', included: true },
            { name: 'Secure File Sharing (up to 5GB)', included: true },
            { name: 'Built-In Chat', included: true },
            { name: 'Analytics Dashboard', included: true },
            { name: 'Real-Time Collaboration', included: true },
            { name: 'Seamless Integrations', included: true },
            { name: 'Priority Support', included: false },
        ],
    },
    {
        id: 'enterprise',
        name: 'Enterprise',
        icon: Building2,
        target: 'For Organizations',
        price: 150,
        description: 'Tailored solutions for large-scale businesses',
        popular: false,
        features: [
            { name: 'Centralized Workspace', included: true },
            { name: 'Task Management', included: true },
            { name: 'Secure File Sharing (up to 5GB)', included: true },
            { name: 'Built-In Chat', included: true },
            { name: 'Analytics Dashboard', included: true },
            { name: 'Real-Time Collaboration', included: true },
            { name: 'Seamless Integrations', included: true },
            { name: 'Priority Support', included: true },
        ],
    },
]

export default function PricingSection() {
    return (
        <div className="bg-gray-50 pt-8 pb-8 lg:pb-4 lg:pt-16 px-4">
            <div className="max-w-6xl mx-auto">
                {/* Header */}
                <div className="text-center mb-8">
                    <div className="inline-flex items-center gap-1 bg-white border border-gray-200 rounded-[6px] px-4 py-2 mb-6">
                        <DollarSign size={14} color="#000000B2" />
                        <span className="text-sm text-[#000000B2]"> Pricing</span>
                    </div>
                    <h5 className="text-[22px] lg:text-[50px] font-semibold lg:font-medium text-black font-serif">
                        Find Your Perfect Plan
                    </h5>
                    <p className="text-[13px] lg:text-[16px] text-[#00000080]">
                        Affordable pricing tailored to your team&apos;s needs.
                    </p>
                </div>

                {/* Pricing Grid */}
                <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 lg:gap-2 xl:gap-4 md:max-w-sm lg:max-w-7xl mx-auto">
                    {pricingPlans.map((plan) => {
                        const IconComponent = plan.icon
                        return (
                            <Card
                                key={plan.id}
                                className={`h-[631px] bg-white border border-[#0000001A] shadow-none rounded-[20px] overflow-hidden px-2 ${plan?.bg}`}>
                                <CardHeader className="p-8 pb-0">
                                    <div className="flex justify-between items-center mb-2">
                                        {/* Plan name */}
                                        <p className="text-xl font-medium text-gray-900 ">{plan.name}</p>
                                        {/* Target audience */}
                                        <div className="flex items-center gap-2 border border-[#0000001A] px-1 py-2 rounded-[6px] bg-[#00000005]">
                                            <IconComponent className="w-4 h-4" color="#000000B2" />
                                            <span className="text-xs text-gray-500">{plan.target}</span>
                                        </div>
                                    </div>

                                    {/* Description */}
                                    <p className="text-[#00000080] text-[16px] mb-8">{plan.description}</p>
                                </CardHeader>

                                <CardContent className="mt-[-40px] px-8 pt-4 pb-6 border rounded-[10px] bg-white">
                                    {/* Price */}
                                    <div className="mb-2 flex items-center gap-2">
                                        <div className="flex items-baseline gap-2">
                                            <h3 className="text-5xl lg:text-4xl xl:text-5xl font-bold text-black">
                                                {plan.price}€
                                            </h3>
                                            <span className="text-[#00000080] text-lg ml-1">/month</span>
                                        </div>

                                        {plan.popular && (
                                            <div>
                                                <Badge className="bg-[#08B38B]  text-white text-xs px-3 py-1 font-medium rounded-full">
                                                    Most Popular
                                                </Badge>
                                            </div>
                                        )}
                                    </div>
                                    <p className="text-xs text-[#00000080]">Billed monthly or annually. Cancel anytime.</p>
                                    <div className="space-y-2 border-t pt-6 mt-6 mb-5 pb-3">
                                        {plan.features.map((feature, index) => (
                                            <div key={index} className="flex items-center gap-3">
                                                <div
                                                    className={`w-[15px] h-[15px] rounded-full flex items-center justify-center flex-shrink-0 ${
                                                        feature.included ? 'bg-[#08B38B]' : 'bg-[#0000001A]'
                                                    }`}>
                                                    {feature.included ? (
                                                        <Check className=" text-black" size={8} />
                                                    ) : (
                                                        <X className="" size={10} color="#0000001A" />
                                                    )}
                                                </div>
                                                <span
                                                    className={`text-sm ${feature.included ? 'text-gray-900' : 'text-gray-400'}`}>
                                                    {feature.name}
                                                </span>
                                            </div>
                                        ))}
                                    </div>

                                    {/* CTA Button */}
                                    <Button className="w-full bg-black hover:bg-gray-800 text-white py-3 rounded-lg text-[14px] font-normal ">
                                        Get Started
                                    </Button>
                                </CardContent>
                            </Card>
                        )
                    })}
                </div>
            </div>
        </div>
    )
}
