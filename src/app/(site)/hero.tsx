import Image from 'next/image'
import Join<PERSON>aitingListCTA from './site-components/join-waiting-list-cta'
import { Badge } from '@/components/ui/badge'
import { ChevronRight } from 'lucide-react'

const Hero = () => {
    return (
        <div className="w-full flex flex-col justify-center items-center gap-2 mt-10 3xl:mt-15 relative">
            <div className="flex justify-center gap-2 border w-fit rounded-lg p-2 mt-4">
                <Image src={'/assets/img/dummy-users.png'} width={500} height={22} alt="Logo" className="h-auto w-[48px]" />
                <p>Join 100+ members</p>
                <Badge variant="secondary" className="text-xs bg-[#08B38B21] rounded-md">
                    Waitlist <ChevronRight size={12} className="ml-1" />{' '}
                </Badge>
            </div>
            <div className="w-full flex flex-col justify-center items-center gap-2 absolute top-18 3xl:top-28">
                <div className="mx-auto w-fit">
                    <div className="font-serif flex justify-center text-center text-[30px] sm:text-6xl 3xl:text-[80px] font-medium">
                        <h1>You Dream. We</h1> <span className="text-[#A8A9AC] pl-1"> Structure</span>
                    </div>
                    <p className="mx-auto max-w-[86%] lg:max-w-3xl text-center sm:text-justify leading-relaxed text-sm sm:text-[16px] text-[#000000B2] py-4">
                        With AI-powered project planning and intelligent workflows, your projects start with clarity, stay on
                        track, and finish strong. No chaos, no guesswork — just structured execution from day one.
                    </p>
                </div>
                <JoinWaitingListCTA
                    buttonClassName="h-[40px] hidden sm:flex px-2 bg-linear-to-r from-[#08B487] to-[#EDBC00]"
                    iconText="Get Started For Free"
                />
                <p className="text-[#433C4E] text-xs italic hidden sm:block">No credit card required.</p>
            </div>
            <Image
                src={'/assets/img/hero-image.png'}
                width={3000}
                height={800}
                alt="Hero Image"
                className="hidden lg:block w-auto h-auto object-cover mt-10 xl:mt-0"
                loading="eager"
                quality={90}
            />
            <Image //for mobile
                src={'/assets/img/hero-bg-mob.png'}
                width={3000}
                height={800}
                alt="Hero Image"
                className="block lg:hidden w-auto h-auto object-cover mt-30 sm:mt-50 md:mt-40"
                loading="eager"
                quality={90}
            />
        </div>
    )
}
export default Hero
