'use client'

import { FC, useState } from 'react'
import { format } from 'date-fns'

import { api } from '@/config/axios-config'
import endpoints from '@/services/api-endpoints'
import { useQuery } from '@tanstack/react-query'
import { toast } from 'sonner'
import { useAuthStore } from '@/store/auth.store'
import EmptyState from '@/components/empty-projects'
import { DataTable } from '@/components/custom-data-table'
import { ColumnDef } from '@tanstack/react-table'
import { Project } from '@/components/project-card'
import CreateProjectModal from '@/components/create-project-modal'
import { ProjectCount } from './page'
import { Progress } from '@/components/ui/progress'
import { Checkbox } from '@/components/ui/checkbox'
import { AssigneeAvatar } from '@/components/assignee-avatar-with-fallback'
import { useRouter } from 'next/navigation'
import DepartmentChip from '@/components/ui/department-chip'
import { Loader } from 'lucide-react'

interface ProjectsGridProps {
    status?: string
    search?: string
    viewType?: 'grid' | 'list'
    debouncedSearchQuery: string
    handleCloseModal: () => void
    isModalOpen: boolean
    handleProjectCount: (data: ProjectCount) => void
    handleCreateProject: () => void
}

const ListView: FC<ProjectsGridProps> = ({
    debouncedSearchQuery,
    isModalOpen,
    handleCloseModal,
    handleProjectCount,
    handleCreateProject,
}) => {
    const [currentPage, setCurrentPage] = useState(1)
    const router = useRouter()

    const currentWorkspace = useAuthStore((state) => state.currentWorkspace)
    const columns: ColumnDef<Project>[] = [
        {
            id: 'select',
            header: ({ table }) => (
                <Checkbox
                    checked={table.getIsAllPageRowsSelected() || (table.getIsSomePageRowsSelected() && 'indeterminate')}
                    onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
                    aria-label="Select all"
                />
            ),
            cell: ({ row }) => (
                <Checkbox
                    checked={row.getIsSelected()}
                    onCheckedChange={(value) => row.toggleSelected(!!value)}
                    aria-label="Select row"
                    id="select"
                />
            ),
            enableSorting: false,
            enableHiding: false,
        },
        {
            accessorKey: 'name',
            header: 'Project Name',
        },
        {
            accessorKey: 'dept',
            header: 'Active Department',
            cell: ({ row }) => {
                return (
                    <div className="flex justify-center items-center gap-2 w-fit">
                        {row.original.departments &&
                            row.original.departments.map((dept) => (
                                <DepartmentChip key={dept.id} shortCode={dept.short_code} label={dept.label} size="sm" />
                            ))}
                    </div>
                )
            },
        },
        {
            accessorKey: 'status.label',
            header: 'Status',
            cell: ({ row }) => {
                const status = row.original.status
                return (
                    <span className="px-2 py-1 rounded text-white text-sm w-fit" style={{ color: status.colour }}>
                        {status.label}
                    </span>
                )
            },
        },

        {
            accessorKey: 'progress.percentage',
            header: 'Progress',
            cell: ({ row }) => {
                const { percentage } = row.original.progress
                return (
                    <>
                        {Number(percentage) >= 0 && (
                            <div className="w-full text-sm mb-1 text-[12px]">
                                <span className="text-[#3C557A] text-xs">
                                    <span className="text-[#087A91]">{percentage}%</span> Complete
                                </span>
                                <Progress value={Number(percentage)} className="h-2 " />
                            </div>
                        )}
                    </>
                )
            },
        },
        {
            accessorKey: 'dueDate',
            header: 'Due Date',
            cell: ({ row }) => {
                const createdAt = row.original.createdAt ?? ''
                const formattedDate = createdAt ? format(new Date(createdAt), 'dd-MM-yyyy') : null

                return `${formattedDate}`
            },
        },
        {
            accessorKey: 'features',
            header: 'Features',
            cell: ({ row }) => {
                const { completed, total } = row.original.features ?? {}
                return `${completed}/${total}`
            },
        },

        {
            accessorKey: 'team',
            header: 'Assignee',
            cell: ({ row }) => {
                const team = row.original.team
                // return team.map(member => `${member.first_name} ${member.last_name}`).join(", ")
                return (
                    <div className="flex -space-x-2">
                        {team?.slice(0, 3)?.map((member) => (
                            <AssigneeAvatar
                                key={member.id}
                                assignee={member.first_name + ' ' + member.last_name}
                                className="border-2 border-white h-[24px] w-[24px]"
                            />
                        ))}
                        {team?.length > 3 && (
                            <AssigneeAvatar
                                className="border-2 border-white h-[24px] w-[24px]"
                                assignee={`+ ${team.length - 3 > 1 ? team.length - 3 + 'Others' : team.length - 3 + 'Other'}`}
                            />
                        )}
                    </div>
                )
            },
        },
    ]

    const { data, isLoading, refetch } = useQuery({
        queryKey: ['projects', { searchQuery: debouncedSearchQuery, workspaceId: currentWorkspace?.id, page: currentPage }],
        queryFn: async () => {
            if (!currentWorkspace?.id)
                return {
                    projectData: [],
                    paginationData: {
                        total: 0,
                        currentPage: 1,
                        totalPages: 1,
                        pageSize: 10,
                    },
                }

            try {
                const response = await api.get(endpoints.project.listProjects, {
                    params: {
                        workspace_id: currentWorkspace.id,
                        searchQuery: debouncedSearchQuery,
                        page: currentPage,
                        limit: 10,
                    },
                })
                handleProjectCount({ ...response.data.data.paginationData, loaded: response.data.data.projectData.length })
                return response.data.data
            } catch (error) {
                console.error('Error fetching projects:', error)
                toast.error('Error fetching projects. Please try again later.')
                throw error
            }
        },
        enabled: !!currentWorkspace?.id,
    })

    const allProjects = data?.projectData || []
    const paginationDetails = data?.paginationData || {
        total: 0,
        currentPage: 1,
        totalPages: 1,
        pageSize: 10,
    }

    const handleNextPage = () => {
        if (currentPage < paginationDetails.totalPages) {
            setCurrentPage((prev) => prev + 1)
        }
    }

    const handlePreviousPage = () => {
        if (currentPage > 1) {
            setCurrentPage((prev) => prev - 1)
        }
    }

    return (
        <>
            {isLoading ? (
                <div className="p-4 w-full flex items-center justify-center h-[40vh]">
                    <Loader className="animate-spin" />
                </div>
            ) : allProjects.length === 0 ? (
                <EmptyState onClick={handleCreateProject} />
            ) : (
                <div>
                    <DataTable
                        columns={columns}
                        data={allProjects}
                        handleNextPage={handleNextPage}
                        handlePreviousPage={handlePreviousPage}
                        paginationDetails={paginationDetails}
                        onRowClick={(row) => {
                            router.push(`projects/${row.id}`) // Your dynamic route
                        }}
                    />
                </div>
            )}

            <CreateProjectModal isOpen={isModalOpen} onClose={handleCloseModal} reFetchProjectList={refetch} />
        </>
    )
}
export default ListView
