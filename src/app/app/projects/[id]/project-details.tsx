'use client'

import { Card, CardContent } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import AvatarGroup from '@/components/avatar-group'
import getStatusIcon from '@/components/get-status-icon'
import { AssigneeAvatar } from '@/components/assignee-avatar-with-fallback'

import { ProjectProgress } from '@/components/project-progress'
import { formatDate } from '@/utils/format-date.utils'
import { ArrowRight } from 'lucide-react'

interface Department {
    id: number
    short_code: string
    label: string
    colour: {
        text: string
        border: string
        background: string
    }
    icon: string
}

interface ProjectData {
    id: number
    name: string
    overview: string
    workspace_id: number
    status_id: number
    stack: Array<string>
    createdAt: string
    updatedAt: string
    deletedAt: null
    created_by: number
    creator: {
        id: number
        first_name: string
        last_name: string
        img_url?: string
    }
    workspace: {
        id: number
        name: string
    }
    status: {
        id: number
        short_code: string
        label: string
        colour: string
    }
    features: {
        completed: number
        total: number
    }
    departments: Department[]
    team: Array<{
        id: number
        first_name: string
        last_name: string
        avatar?: string
    }>
    estimated_effort: string
    timeline: {
        start_date: string
        end_date: string
        days: number
    }
    priority: {
        level: string
        color: string
    }
    progress: {
        percentage: number
        completed: number
        total: number
    }
}

export function ProjectDetails({ project }: { project: ProjectData }) {
    const projectDataForProgress = {
        title: project.name,
        progressPercentage: project.progress.percentage,
        currentDay: 3,
        totalDays: 5,
        isOnTrack: true,
        metrics: {
            features: project.features?.total,
            tasks: project.progress.total, //total tasks
            teamMembers: project.team?.length,
        },
    }

    return (
        <>
            <div className="flex flex-col gap-2">
                <ProjectProgress {...projectDataForProgress} />
            </div>
            <Card className="w-full shadow-none border-none bg-transparent pt-0">
                <CardContent className="p-4">
                    <div className="space-y-4">
                        <div className="grid grid-cols-[auto_1fr] gap-x-4 gap-y-4 text-[#3C557A] text-xs">
                            <div className="text-xs">Status</div>
                            <div className="flex items-center">
                                <div className="flex items-center gap-2">
                                    <div>{getStatusIcon(project.status.label)}</div>
                                    <span className="text-xs">{project.status.label}</span>
                                </div>
                            </div>

                            <div className="text-xs">Team members</div>
                            <div className="flex items-center">
                                <div className="flex -space-x-2">
                                    <AvatarGroup team={project.team} />
                                </div>
                            </div>

                            <div className="text-xs">Est. Effort</div>
                            {project?.estimated_effort && <div className="text-xs">{project?.estimated_effort} Hours</div>}

                            {project?.timeline && (
                                <>
                                    <div className="text-xs">Projected Timeline</div>
                                    <div className="text-xs flex items-center gap-2">
                                        {formatDate(project?.timeline?.start_date, 'DD-MM-YYYY')}{' '}
                                        <ArrowRight className="h-4 w-4" /> {formatDate(project?.timeline?.end_date, 'DD-MM-YYYY')}{' '}
                                        ({project?.timeline?.days} Days)
                                    </div>
                                </>
                            )}
                            <div className="text-xs">Tech Stack</div>
                            <div className="flex flex-wrap gap-2">
                                {project?.stack.length > 0 &&
                                    project?.stack?.map((tech: string, index: number) => (
                                        <Badge key={index} variant="outline" className="text-xs bg-[#F1F5F9]">
                                            {tech}
                                        </Badge>
                                    ))}
                            </div>

                            <div className="text-xs">Created by</div>
                            <div className="flex items-center gap-2">
                                <AssigneeAvatar
                                    assignee={project.creator.first_name + ' ' + project.creator.last_name}
                                    imageUrl={project.creator.img_url}
                                />
                                <span className="text-xs">
                                    {project.creator.first_name + ' ' + project.creator.last_name} on{' '}
                                    {new Date(project.createdAt).toLocaleDateString('en-US', {
                                        month: '2-digit',
                                        day: '2-digit',
                                        year: 'numeric',
                                    })}
                                </span>
                            </div>
                        </div>
                        <div className="space-y-2 max-w-110">
                            <div className="text-xs text-[#3C557A]">Description</div>
                            <div className="text- text-[#9095A1]">{project.overview}</div>
                        </div>
                    </div>
                </CardContent>
            </Card>
        </>
    )
}
