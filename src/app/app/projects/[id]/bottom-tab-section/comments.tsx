'use client'

import { useState } from 'react'
import { ArrowBigUp, Reply, Send } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { AssigneeAvatar } from '@/components/assignee-avatar-with-fallback'

interface Comment {
    id: number
    user: {
        name: string
        avatar: string
    }
    text: string
    upvotes: number
    replies: number
}

const sampleComments: Comment[] = [
    {
        id: 1,
        user: {
            name: '<PERSON>',
            avatar: '',
        },
        text: 'Can we use anima plugin for the conversion',
        upvotes: 0,
        replies: 0,
    },
    {
        id: 2,
        user: {
            name: 'joe',
            avatar: '',
        },
        text: 'Try out the best !',
        upvotes: 0,
        replies: 0,
    },
]
const CommentsTab = () => {
    return <CommentThread />
}

export default CommentsTab

function CommentThread() {
    const [comments, setComments] = useState<Comment[]>(sampleComments)
    const [newComment, setNewComment] = useState('')

    const handleAddComment = () => {
        if (newComment.trim()) {
            const comment: Comment = {
                id: comments.length + 1,
                user: {
                    name: 'You',
                    avatar: '',
                },
                text: newComment,
                upvotes: 0,
                replies: 0,
            }
            setComments([...comments, comment])
            setNewComment('')
        }
    }

    const handleUpvote = (commentId: number) => {
        setComments(
            comments.map((comment) => (comment.id === commentId ? { ...comment, upvotes: comment.upvotes + 1 } : comment)),
        )
    }

    return (
        <div className="w-full mx-auto p-4 bg-transparent">
            <div className="-space-y-5">
                {comments.map((comment) => (
                    <div key={comment.id} className="flex gap-3 border py-6 px-2 rounded-md">
                        <div className="flex-shrink-0">
                            <AssigneeAvatar
                                assignee={comment.user.name}
                                imageUrl={comment.user.avatar}
                                className="border-2 border-white h-[30px] w-[30px]"
                            />
                        </div>
                        <div className="flex-1 space-y-2">
                            <div className="text-sm text-gray-600">
                                <span className="font-medium text-gray-900">{comment.user.name}</span> commented on{' '}
                                <span className="text-blue-600 hover:underline cursor-pointer">this task</span>
                            </div>
                            <div className="text-gray-900">{comment.text}</div>
                            <div className="flex items-center gap-4 text-sm">
                                <button
                                    onClick={() => handleUpvote(comment.id)}
                                    className="flex items-center gap-1 text-gray-500 hover:text-green-600 transition-colors">
                                    <ArrowBigUp fill="#2FA87A" className="w-6 h-6" color="#2FA87A" />
                                    <span>upvote</span>
                                </button>
                                <button className="flex items-center gap-1 text-gray-500 hover:text-gray-700 transition-colors">
                                    <Reply className="w-4 h-4" />
                                    <span>replies</span>
                                </button>
                            </div>
                        </div>
                    </div>
                ))}
            </div>

            <div className="mt-6 flex gap-3 border px-2 pt-1 rounded-lg">
                <div className="flex-1 flex gap-2">
                    <Input
                        value={newComment}
                        onChange={(e) => setNewComment(e.target.value)}
                        placeholder="Add comment..."
                        className="flex-1 border-none rounded-none px-0 focus-visible:ring-0 focus-visible:border-gray-400"
                    />
                    <Button
                        onClick={handleAddComment}
                        size="sm"
                        className="rounded-full w-8 h-8 p-0 bg-gray-900 hover:bg-gray-800">
                        <Send className="w-4 h-4" />
                    </Button>
                </div>
            </div>
        </div>
    )
}
