import { AssigneeAvatar } from '@/components/assignee-avatar-with-fallback'
import AvatarGroup from '@/components/avatar-group'
import { Button } from '@/components/ui/button'
import { Card } from '@/components/ui/card'
import { Trash2 } from 'lucide-react'
import Image from 'next/image'

type WorkspaceType = {
    id: number
    name: string
    img_url: string
    invite_link: string
    plan: number
    createdAt: string
    creator: {
        id: number
        first_name: string
        last_name: string
        img_url: string
    }
    members: {
        id: number
        first_name: string
        last_name: string
        img_url: string
    }[]
}

const WorkspaceCard = ({ workspace }: { workspace: WorkspaceType }) => {
    return (
        <Card className="pl-8">
            <div className="flex pr-6">
                <div className="flex flex-col gap-2 flex-1 py-4">
                    <div className="flex justify-between items-center">
                        <div className="flex gap-2 items-center">
                            <div className="w-[40px] h-[40px] rounded-[8px] bg-gray-200 border-2 border-white overflow-hidden">
                                {workspace.img_url && (
                                    <Image
                                        src={workspace.img_url}
                                        alt="Workspace image"
                                        width={32}
                                        height={32}
                                        className="object-cover w-full h-full"
                                    />
                                )}
                            </div>
                            <div className="flex flex-col">
                                <div className="text-sm font-medium">{workspace.name}</div>
                            </div>
                        </div>
                    </div>
                </div>
                <div className="flex items-center gap-2">
                    <Button variant="outline" size="icon" className="text-xs text-[#3C557A] bg-white">
                        <Trash2 />
                    </Button>
                    <Button variant="outline" className=" text-xs text-[#0F172A]">
                        Edit Workspace
                    </Button>
                </div>
            </div>
            <div className="flex">
                <div className="text-xs text-[#3C557A] w-40">Team Members</div>
                <AvatarGroup team={workspace.members} />
            </div>
            <div className="flex">
                <div className="text-xs text-[#3C557A] w-40">Date Created</div>
                <div className="text-xs text-[#3C557A]">
                    {new Date(workspace.createdAt).toLocaleDateString('en-US', {
                        month: '2-digit',
                        day: '2-digit',
                        year: 'numeric',
                    })}
                </div>
            </div>
            <div className="flex">
                <div className="text-xs text-[#3C557A] w-40">Admin</div>
                <div className="flex items-center gap-2">
                    <AssigneeAvatar
                        assignee={workspace.creator.first_name + ' ' + workspace.creator.last_name}
                        imageUrl={workspace.creator.img_url}
                    />
                    <div className="text-xs text-[#3C557A]">
                        {workspace.creator.first_name + ' ' + workspace.creator.last_name}
                    </div>
                </div>
            </div>
        </Card>
    )
}

export default WorkspaceCard
