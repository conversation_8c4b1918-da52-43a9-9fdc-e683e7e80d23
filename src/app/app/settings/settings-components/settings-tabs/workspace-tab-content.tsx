import WorkspaceCard from '../workspace-card'

const workspaces = [
    {
        id: 1,
        name: 'Workspace 1',
        img_url: 'http://82.112.237.192:9000/raydian/e0967aa4-7200-4b35-92e9-9fbb83c53739.png',
        invite_link: '',
        plan: 1,
        createdAt: '2025-05-20T10:00:00.000Z',
        creator: {
            id: 1,
            first_name: '<PERSON>',
            last_name: '<PERSON>',
            img_url: 'https://example.com/images/joel.jpg',
        },
        members: [
            {
                id: 1,
                first_name: '<PERSON>',
                last_name: '<PERSON>',
                img_url: 'https://example.com/images/joel.jpg',
            },
            {
                id: 2,
                first_name: '<PERSON>',
                last_name: '<PERSON>',
                img_url: 'https://example.com/images/albert.jpg',
            },
            {
                id: 3,
                first_name: '<PERSON>',
                last_name: '<PERSON>',
                img_url: 'https://example.com/images/sofia.jpg',
            },
        ],
    },
    {
        id: 2,
        name: 'Workspace 2',
        img_url: '',
        invite_link: '',
        plan: 1,
        createdAt: '2025-05-20T10:00:00.000Z',
        creator: {
            id: 1,
            first_name: '<PERSON>',
            last_name: 'Joy',
            img_url: 'https://example.com/images/joel.jpg',
        },
        members: [
            {
                id: 1,
                first_name: '<PERSON>',
                last_name: 'Joy',
                img_url: 'https://example.com/images/joel.jpg',
            },
            {
                id: 2,
                first_name: 'Albert',
                last_name: 'Ben',
                img_url: 'https://example.com/images/albert.jpg',
            },
            {
                id: 3,
                first_name: 'Sofia',
                last_name: 'Thomas',
                img_url: 'https://example.com/images/sofia.jpg',
            },
        ],
    },
]

const WorkspaceTabContent = () => {
    return (
        <div className="space-y-4">
            {workspaces.map((workspace) => (
                <WorkspaceCard key={workspace.id} workspace={workspace} />
            ))}
        </div>
    )
}

export default WorkspaceTabContent
