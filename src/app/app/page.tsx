'use client'

import { useState, useEffect, useRef, ChangeEvent, KeyboardEvent } from 'react'
import { motion } from 'framer-motion'
import { ClipboardIcon, SendIcon, ZapIcon, CheckCircleIcon, Loader2Icon, FolderIcon, RefreshCcw } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Textarea } from '@/components/textarea'
import { useBreadcrumbStore } from '@/store/breadcrumb.store'
import { useAuthStore } from '@/store/auth.store'
import { MessageBubble } from '@/components/chat/message-bubble'
import TaskCard from '@/components/scratchpad-task-card'
import { useAIAgent } from '@/hooks/use-ai-agent'
import { PROJECT_TEMPLATES } from '@/constants/project-templates'
import { ScrollArea } from '@/components/ui/scroll-area'
import { ProjectSelector } from '@/components/project-selector'
import { CHAT_SUGGESTIONS } from '@/constants/chat-suggestions'
import { toast } from 'sonner'

const PROMPT_LIMIT = 250

const ProjectCreationAgent = () => {
    const { setBreadcrumbs } = useBreadcrumbStore()
    const { fetchMyDetails, currentWorkspace } = useAuthStore()

    const [inputValue, setInputValue] = useState('')
    const [promptCount] = useState(250)
    const chatContainerRef = useRef<HTMLDivElement>(null)
    const tasksContainerRef = useRef<HTMLDivElement>(null)
    const textareaRef = useRef<HTMLTextAreaElement>(null)

    const {
        messages,
        tasks,
        selectedProject,
        showTasksPanel,
        isLoading,
        isSaving,
        sendMessage,
        generateTasks,
        publishTasks,
        resetProject,
        hasMessages,
    } = useAIAgent()

    useEffect(() => {
        if (currentWorkspace?.id) {
            resetProject()
        }
    }, [currentWorkspace?.id, resetProject])

    const SUGGESTIONS_TEMPLATES = selectedProject ? PROJECT_TEMPLATES : CHAT_SUGGESTIONS

    useEffect(() => {
        setBreadcrumbs([
            { label: 'Dashboard', href: '/app' },
            { label: 'Chat', href: '/app' },
        ])
        fetchMyDetails()
    }, [setBreadcrumbs, fetchMyDetails])

    useEffect(() => {
        if (chatContainerRef.current) {
            requestAnimationFrame(() => {
                const scrollContainer = chatContainerRef.current?.querySelector('[data-radix-scroll-area-viewport]')
                if (scrollContainer) {
                    scrollContainer.scrollTop = scrollContainer.scrollHeight
                }
            })
        }
    }, [messages])

    useEffect(() => {
        if (tasksContainerRef.current) {
            requestAnimationFrame(() => {
                const scrollContainer = tasksContainerRef.current?.querySelector('[data-radix-scroll-area-viewport]')
                if (scrollContainer) {
                    scrollContainer.scrollTop = scrollContainer.scrollHeight
                }
            })
        }
    }, [tasks])

    const handleSendMessage = async () => {
        if (!inputValue.trim()) return

        if (!selectedProject) {
            toast.error('Please select a project before starting a conversation.')
            return
        }

        await sendMessage(inputValue)
        setInputValue('')
        setFocusForTextarea()
    }

    const handleKeyDown = (e: KeyboardEvent<HTMLTextAreaElement>) => {
        if (e.key === 'Enter' && !e.shiftKey) {
            e.preventDefault()
            handleSendMessage()
        }
    }

    const handleTemplateClick = (template: string) => {
        setInputValue(template)
        setFocusForTextarea()
    }

    const setFocusForTextarea = () => {
        setTimeout(() => {
            textareaRef.current?.focus()
        }, 100)
    }

    const handleGenerateTasks = async () => {
        await generateTasks()
        setFocusForTextarea()
    }

    const taskInputBottom = (
        <>
            {hasMessages ? (
                <Button
                    variant="outline"
                    size="sm"
                    className="flex items-center gap-2"
                    onClick={handleGenerateTasks}
                    disabled={isLoading}>
                    <ClipboardIcon className="h-4 w-4" />
                    <span className="text-sm">Generate Tasks</span>
                </Button>
            ) : (
                <div className="relative">
                    <ProjectSelector
                        onProjectSelect={() => setFocusForTextarea()}
                        onCreateNew={() => setFocusForTextarea()}
                        className="w-auto"
                    />
                </div>
            )}
        </>
    )

    return (
        <div className={`flex h-[90vh] p-4 ${hasMessages ? '' : 'justify-center items-center'}`}>
            <div
                className={`rounded-xl p-6 flex flex-col transition-all duration-500 ease-in-out w-full max-w-2xl
        ${
            !hasMessages
                ? 'w-96 max-w-2xl mx-auto my-8'
                : showTasksPanel && selectedProject
                ? 'w-1/2 mr-4 '
                : 'w-2/3 max-w-2/3 mx-auto bg-none'
        }
        ${!hasMessages ? "bg-[url('/assets/bg/chat-bg.png')] bg-center bg-cover bg-no-repeat" : 'bg-none'}
      `}>
                <div className="text-md mb-3 flex flex-col justify-center h-[calc(100dvh-12rem)]">
                    {!hasMessages && (
                        <>
                            <motion.h2
                                initial={{ opacity: 0 }}
                                animate={{ opacity: 1 }}
                                transition={{ delay: 0.2 }}
                                className="text-gray-500 font-light dark:text-gray-400 mb-2">
                                Welcome to Raydian AI
                            </motion.h2>
                            <motion.h1
                                initial={{ opacity: 0 }}
                                animate={{ opacity: 1 }}
                                transition={{ delay: 0.3 }}
                                className="text-3xl font-medium mb-4 flex flex-row">
                                <span className="text-3xl font-light bg-gradient-to-r from-[#08698F] to-[#68CC58] text-transparent bg-clip-text">
                                    {'Project Planner | Task Insights'}
                                </span>
                            </motion.h1>
                        </>
                    )}

                    {hasMessages && selectedProject && (
                        <motion.div
                            initial={{ opacity: 0, y: -10 }}
                            animate={{ opacity: 1, y: 0 }}
                            transition={{ delay: 0.4 }}
                            className="mb-4">
                            <div className="bg-blue-50 dark:bg-blue-900/20 rounded-lg p-3 border border-blue-200 dark:border-emerald-700">
                                <div className="flex items-center gap-2 text-sm">
                                    <FolderIcon className="h-4 w-4 text-emerald-600" />
                                    <span className="text-gray-700 dark:text-gray-300">Tasks will be added to:</span>
                                    <span className="font-medium text-emerald-700 dark:text-blue-300">
                                        {selectedProject.name}
                                    </span>
                                    <span className="text-xs text-gray-500">({selectedProject.workspace.name})</span>
                                </div>
                            </div>
                        </motion.div>
                    )}

                    {hasMessages && (
                        <ScrollArea ref={chatContainerRef} type="scroll" className="h-[60vh] mb-4 pr-2 space-y-3 flex flex-col">
                            {messages.map((message) => (
                                <MessageBubble key={message.id} message={message} />
                            ))}
                        </ScrollArea>
                    )}

                    <motion.div
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ delay: 0.3 }}
                        className={`h-[180px] flex flex-col justify-between mt-4 ${
                            hasMessages
                                ? 'bottom-0 border-t border-gray-100 rounded-t-xl px-3 py-2 bg-white shadow-md border'
                                : 'border border-gray-100 rounded-xl px-3 py-2 bg-white shadow-md'
                        }`}>
                        <div className="relative h-full mb-2 border-b border-gray-200 dark:border-gray-700">
                            <Textarea
                                ref={textareaRef}
                                className="w-full text-md text-gray-600 resize-none dark:text-gray-300 bg-transparent border-none focus-visible:ring-0 focus-visible:ring-offset-0 pl-0"
                                placeholder={selectedProject ? 'How can I help you with your project?' : 'How can I help you?'}
                                value={inputValue}
                                onChange={(e: ChangeEvent<HTMLTextAreaElement>) => setInputValue(e.target.value)}
                                onKeyDown={handleKeyDown}
                                disabled={isLoading}
                            />
                        </div>

                        <div className="flex items-center justify-between">
                            <div className="flex space-x-4">{taskInputBottom}</div>
                            <Button
                                size="icon"
                                className="bg-gray-950 dark:bg-gray-700 hover:bg-gray-800 dark:hover:bg-gray-600 text-white rounded-md"
                                onClick={handleSendMessage}
                                disabled={isLoading || !inputValue.trim()}>
                                <SendIcon className="h-4 w-4" />
                            </Button>
                        </div>
                    </motion.div>

                    {!hasMessages && (
                        <motion.div initial="hidden" animate="show" className="mt-2 flex flex-row gap-2">
                            <motion.div className="flex flex-row w-full items-center justify-between">
                                {SUGGESTIONS_TEMPLATES.slice(0, 3).map((text, index) => (
                                    <motion.div
                                        key={index}
                                        className="flex w-fit cursor-pointer bg-white rounded-xl p-2 text-xs text-[#666F8D] border border-dashed border-[#43424217]"
                                        onClick={() => handleTemplateClick(text)}>
                                        {text}
                                    </motion.div>
                                ))}
                                <RefreshCcw className="h-4 w-4 text-[#6C7491] cursor-pointer" />
                            </motion.div>
                        </motion.div>
                    )}

                    <motion.div
                        initial={{ opacity: 0 }}
                        animate={{ opacity: 1 }}
                        transition={{ delay: 0.8 }}
                        className="mt-2 flex items-center text-sm text-gray-500 dark:text-gray-400">
                        <ZapIcon className="h-4 w-4 mr-2" />
                        <span>
                            {promptCount}/{PROMPT_LIMIT} Prompts
                        </span>
                    </motion.div>
                </div>
            </div>

            {showTasksPanel && selectedProject && (
                <motion.div
                    initial={{ opacity: 0, x: 20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ duration: 0.5 }}
                    className="w-1/2 rounded-xl p-6 bg-gray-50 dark:bg-gray-900 flex flex-col mx-auto overflow-hidden max-h-[90vh]">
                    <div className="flex justify-between items-center mb-4">
                        <h2 className="text-xl font-medium">
                            Project Tasks ({tasks.length})
                            {selectedProject && (
                                <span className="text-sm font-normal text-gray-500 ml-2">→ {selectedProject.name}</span>
                            )}
                        </h2>
                        <Button
                            variant="outline"
                            size="sm"
                            onClick={publishTasks}
                            disabled={isSaving || tasks.length === 0}
                            className="flex items-center gap-2">
                            {isSaving ? (
                                <Loader2Icon className="h-4 w-4 mr-2 animate-spin" />
                            ) : (
                                <CheckCircleIcon className="h-4 w-4 mr-2" />
                            )}
                            <span>{isSaving ? 'Saving...' : 'Publish'}</span>
                        </Button>
                    </div>

                    <ScrollArea ref={tasksContainerRef} className="flex-grow overflow-y-auto pr-2 ">
                        {tasks.map((task, index) => (
                            <TaskCard
                                key={task.id}
                                taskNum={index + 1}
                                id={task.id}
                                category={task.category}
                                title={task.title}
                                description={task.description}
                                assignedRole={task.assignedRole}
                                estimatedHours={task.estimatedHours}
                                dependencies={task.dependencies}
                            />
                        ))}
                    </ScrollArea>
                </motion.div>
            )}
        </div>
    )
}

export default ProjectCreationAgent
