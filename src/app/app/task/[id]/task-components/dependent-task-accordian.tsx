import { Accordion, Accordion<PERSON>ontent, Accordion<PERSON><PERSON>, AccordionTrigger } from '@/components/ui/accordion'
import DependentTaskIcon from '../../../../../../public/assets/icons/dependent-task-icon.png'
import Image from 'next/image'
import DepartmentChip from '@/components/ui/department-chip'
import getStatusIcon from '@/components/get-status-icon'
import getStatusStyles from '@/utils/get-status-styles.utils'
import { cn } from '@/lib/utils'
import { useState } from 'react'
import { Link2 } from 'lucide-react'
import { ScrollArea } from '@/components/ui/scroll-area'
import AvatarGroup from '@/components/avatar-group'

type TaskDepartmentType = {
    id: number
    short_code: string
    label: string
}
type DependentTaskType = {
    id: number
    short_code: string
    title: string
    status_id: number
    taskStatus: {
        id: number
        short_code: string
        label: string
    }
    task_dependency_mapping: {
        dependency_level: number
    }
    taskDepartment?: TaskDepartmentType
    taskAssignees?:
        | [
              {
                  id: number
                  first_name: string
                  last_name: string
                  img_url?: string
              },
          ]
        | []
}

type DependentTasks = DependentTaskType[]

export default function DependentTaskAccordian({ dependentTasks }: { dependentTasks: DependentTasks }) {
    const [isOpen, setIsOpen] = useState(false)
    const formatTaskShortCodes = (tasks: DependentTasks) => {
        const codes = tasks.map((task) => task.short_code)

        if (codes.length === 0) return ''
        if (codes.length === 1) return codes[0]
        if (codes.length === 2) return `${codes[0]} and ${codes[1]}`

        const allButLast = codes.slice(0, -1).join(', ')
        const last = codes[codes.length - 1]
        return `${allButLast} and ${last}`
    }
    return (
        <Accordion type="single" collapsible className="w-full border-none rounded-[10px] ">
            <AccordionItem value="item-1" className="space-y-2">
                <AccordionTrigger className="hover:no-underline cursor-pointer border px-4 " onClick={() => setIsOpen(!isOpen)}>
                    <div className="flex items-center gap-2">
                        <div>
                            <Image src={DependentTaskIcon} alt="Dependent Task Icon" width={32} className="h-auto" />
                        </div>
                        <div className="flex flex-col">
                            <p className="text-[12px] text-[#5C5F62]">{formatTaskShortCodes(dependentTasks)}</p>

                            <p className="text-xs text-[#1C1C1C66]">Dependent Tasks</p>
                        </div>
                    </div>
                </AccordionTrigger>
                <div className={isOpen ? 'border border-[#E1E1E13B] bg-[#FFFFFF] rounded-md py-4 shadow-sm ' : 'hidden'}>
                    <div className="flex mb-2 items-center gap-2 px-4">
                        <div className="border rounded-sm p-1 bg-[#F8F8F8]">
                            <Link2 size={13} color="#9B9B9B" />
                        </div>
                        <p className="text-sm text-[#09090B] font-medium">Depenedent tasks Linked ({dependentTasks.length})</p>
                    </div>
                    <ScrollArea className={dependentTasks.length >= 2 ? 'h-60' : 'h-25'} type="scroll">
                        {dependentTasks.map((task, index) => (
                            <AccordionContent className={index === 0 ? 'p-4' : 'p-4 border-t border-[#EAEAEA]'} key={task.id}>
                                <DependentTaskItem task={task} />
                            </AccordionContent>
                        ))}
                    </ScrollArea>
                </div>
            </AccordionItem>
        </Accordion>
    )
}

const DependentTaskItem = ({ task }: { task: DependentTaskType }) => {
    return (
        <div className="flex items-center justify-between">
            <div className="space-y-2">
                {task?.taskDepartment?.label && (
                    <DepartmentChip shortCode={task?.taskDepartment?.short_code} label={task?.short_code} size="sm" />
                )}
                <p className="text-sm text-[#000] font-medium">{task.title}</p>
                <div className="flex items-center justify-between">
                    <p className="text-xs text-[#3C557A]">Assignee</p>
                    <AvatarGroup team={task?.taskAssignees} className="h-[28px] w-[28px]" />
                </div>
            </div>
            <div className="gap-1 flex items-center">
                <div>{getStatusIcon(task.taskStatus.label)}</div>
                <p className={cn('text-xs font-medium w-fit  rounded-md', getStatusStyles(task.taskStatus.label).text)}>
                    {task.taskStatus.label}
                </p>
            </div>
        </div>
    )
}
