'use client'

import { useState } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Share2, Star, Calendar, Users, Tag, User, OctagonAlert, ToggleLeft, Flag, CircleDashed, Zap } from 'lucide-react'
import AvatarGroup from '@/components/avatar-group'
import { formatDate } from '@/utils/format-date.utils'
import { AssigneeAvatar } from '@/components/assignee-avatar-with-fallback'
import DepartmentChip from '@/components/ui/department-chip'
import { StatusSelect } from './select-task-status'
import endpoints from '@/services/api-endpoints'
import { toast } from 'sonner'
import { useRouter } from 'next/navigation'
import TaskActionsDropdown from './task-action-dropdown'

// Define the task data structure

type TaskDepartmentType = {
    id: number
    short_code: string
    label: string
}
interface Assignee {
    id: number
    first_name: string
    last_name: string
    img_url?: string
}
type DependentTaskType = {
    id: number
    short_code: string
    title: string
    status_id: number
    taskStatus: {
        id: number
        short_code: string
        label: string
    }
    task_dependency_mapping: {
        dependency_level: number
    }
    taskDepartment?: TaskDepartmentType
}

type DependentTasks = DependentTaskType[]
export interface TaskData {
    id: number
    feat_id: number
    short_code: string
    title: string
    description: string
    time_estimate_hrs: number
    start_date: string
    due_date: string
    status_id: number
    assignee: number
    created_by: number
    createdAt: string
    updatedAt: string
    deletedAt: string | null
    department: number | null
    feature: {
        id: number
        feat_code: string
        title: string
        project: {
            id: number
            name: string
        }
    }
    taskStatus: {
        id: number
        short_code: string
        label: string
        colour: string
    }
    assignedUser: Assignee
    creator: {
        id: number
        first_name: string
        last_name: string
        img_url?: string
    }
    taskDepartment: TaskDepartmentType
    priority?: string
    column?: number
    dueDate?: string
    taskPriority?: {
        id?: number
        label: string
        color?: string
    }
    dependentTasks?: DependentTasks
    bug: boolean
    project: {
        id: number
        name: string
    }
    parentTasks?: DependentTasks
    taskAssignees?:
        | [
              {
                  id: number
                  first_name: string
                  last_name: string
                  img_url?: string
              },
          ]
        | []
}

export default function TaskDetailsCard({ taskData, refetchTask }: { taskData: TaskData; refetchTask: () => void }) {
    const [isStarred, setIsStarred] = useState(false)
    const router = useRouter()

    const onAftedDeleteSuccess = () => {
        toast.success('Task deleted successfully')
        router.push('/app/planner')
    }

    const updateDefaultValues = {
        shortCode: taskData.short_code,
        taskId: taskData.id,
        taskName: taskData.title,
        project: String(taskData.feature.project.id),
        feature: String(taskData.feature.id),
        teamMembers: taskData.taskAssignees?.map((assignee: Assignee) => assignee.id),
        department: String(taskData.taskDepartment?.id),
        status: String(taskData.taskStatus.id),
        priority: String(taskData.taskPriority?.id),
        dueDate: new Date(taskData.due_date),
        description: taskData.description,
        isBug: taskData.bug,
        parentTaskId: taskData?.parentTasks?.map((task: DependentTaskType) => task.short_code).join(','),
        projectName: taskData.feature.project.name,
        featureName: taskData.feature.title,
        assignees: taskData.taskAssignees,
        departmentName: taskData.taskDepartment?.label,
        statusName: taskData.taskStatus.label,
        priorityName: taskData.taskPriority?.label,
        taskassigneesArray: taskData.taskAssignees,
    }

    return (
        <Card className="w-full max-w-[322px] shadow-none bg-transparent rounded-[10px] pt-2 gap-0">
            <CardHeader className="p-0">
                <div className="flex flex-row items-center justify-between px-4 pb-2 space-y-0 border-b">
                    <CardTitle className="text-[16px] text-[#64748B] font-medium">Task Details</CardTitle>
                    <div className="flex items-center space-x-2 w-fit">
                        <Button variant="ghost" size="icon" className="h-8 w-8">
                            <Share2 className="h-4 w-4" color="#5B6871" fill="#5B6871" />
                            <span className="sr-only">Share</span>
                        </Button>
                        <Button variant="ghost" size="icon" className="h-8 w-8" onClick={() => setIsStarred(!isStarred)}>
                            <Star
                                color={isStarred ? 'yellow-400' : '#5B6871'}
                                className={`h-4 w-4 ${isStarred ? 'fill-yellow-400 text-yellow-400' : 'fill-[#5B6871]'}`}
                            />
                            <span className="sr-only">Star</span>
                        </Button>
                        <TaskActionsDropdown
                            updateDefaultValues={updateDefaultValues}
                            refetchTask={refetchTask}
                            taskData={taskData}
                            onAftedDeleteSuccess={onAftedDeleteSuccess}
                        />
                    </div>
                </div>
            </CardHeader>
            <CardContent className="pt-0">
                <div className="space-y-3">
                    <div className="grid grid-cols-2 items-center py-1">
                        <div className="flex items-center text-sm text-[#64748B]">
                            <CircleDashed className="mr-2 h-4 w-4" />
                            Status
                        </div>
                        <div className="flex items-center">
                            <StatusSelect
                                entityId={taskData?.taskStatus.id}
                                entityType="task"
                                initialStatusId={taskData?.taskStatus?.id}
                                fetchEndpoint={endpoints.meta.getStatuses}
                                updateEndpoint={`${endpoints.tasks.updateTask}/${taskData.id}`}
                                className="w-[120px] h-[22px] rounded-[6px]"
                                onStatusChange={refetchTask}
                            />
                        </div>
                    </div>

                    <div className="grid grid-cols-2 items-center  py-1">
                        <div className="flex items-center text-sm text-[#64748B]">
                            <OctagonAlert className="mr-2 h-4 w-4" />
                            Priority
                        </div>
                        {taskData?.taskPriority && (
                            <div className="flex items-center">
                                <Badge className="text-xs shadow-xs p-1 rounded-sm" variant="outline">
                                    {taskData?.taskPriority?.label?.toLowerCase() === 'high' && (
                                        <Zap className="mr-1.5 h-4 w-3 " fill="#E35422" color="#E35422" />
                                    )}
                                    <p className="text-[#414651]">{taskData?.taskPriority?.label}</p>
                                </Badge>
                            </div>
                        )}
                    </div>

                    <div className="grid grid-cols-2 items-center py-1">
                        <div className="flex items-center text-sm text-[#64748B]">
                            <Calendar className="mr-2 h-4 w-4" />
                            Start Date
                        </div>
                        <div className="text-sm">{formatDate(taskData?.start_date, 'DD-MM-YYYY')}</div>
                    </div>

                    <div className="grid grid-cols-2 items-center py-1">
                        <div className="flex items-center text-sm text-[#64748B]">
                            <Calendar className="mr-2 h-4 w-4" />
                            Due Date
                        </div>
                        <div className="text-sm">{formatDate(taskData?.due_date, 'DD-MM-YYYY')}</div>
                    </div>

                    <div className="grid grid-cols-2 items-center py-1">
                        <div className="flex items-center text-sm text-[#64748B]">
                            <Users className="mr-2 h-4 w-4" />
                            Assignee
                        </div>
                        <div className="flex items-center">
                            <AvatarGroup team={taskData?.taskAssignees} />
                        </div>
                    </div>

                    <div className="grid grid-cols-2 items-center  py-1">
                        <div className="flex items-center text-sm text-[#64748B]">
                            <Flag className="mr-2 h-4 w-4" />
                            Project
                        </div>
                        <div className="text-sm">{taskData?.feature?.project?.name}</div>
                    </div>

                    <div className="grid grid-cols-2 items-center py-1">
                        <div className="flex items-center text-sm text-[#64748B]">
                            <ToggleLeft className="mr-2 h-4 w-4 rotate-90" />
                            Feature
                        </div>
                        <div className="text-sm">{taskData?.feature?.title}</div>
                    </div>

                    <div className="grid grid-cols-2 items-center py-1">
                        <div className="flex items-center text-sm text-[#64748B]">
                            <Tag className="mr-2 h-4 w-4" />
                            Type
                        </div>
                        {taskData?.taskDepartment && (
                            <div>
                                <DepartmentChip
                                    shortCode={taskData?.taskDepartment?.short_code}
                                    label={taskData?.taskDepartment?.label}
                                    size="sm"
                                />
                            </div>
                        )}
                    </div>

                    <div className="grid grid-cols-2 items-center py-1">
                        <div className="flex items-center text-sm text-[#64748B]">
                            <User className="mr-2 h-4 w-4" />
                            Creator
                        </div>
                        <div className="flex items-center gap-2">
                            <AssigneeAvatar
                                assignee={taskData?.creator?.first_name + ' ' + taskData?.creator?.last_name}
                                imageUrl={taskData?.creator?.img_url}
                            />
                            <span className="text-sm">{taskData?.creator?.first_name + ' ' + taskData?.creator?.last_name}</span>
                        </div>
                    </div>
                </div>
            </CardContent>
        </Card>
    )
}
