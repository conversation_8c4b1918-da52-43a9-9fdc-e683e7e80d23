import TaskCard from '@/components/scratchpad-task-card'
import { SearchableDropdown } from '@/components/searchable-dropdown'
import { But<PERSON> } from '@/components/ui/button'
import { ScrollArea } from '@/components/ui/scroll-area'
import { dummyTaskData, featureList } from '@/data/scratchpad-data'
import { Pencil } from 'lucide-react'
import Image from 'next/image'
import { useState } from 'react'

export default function FeatureHistory() {
    const [expanded, setExpanded] = useState(false)

    return (
        <div className="flex flex-col w-full mt-[0px]">
            <Button variant={'outline'} className="absolute right-0 top-[-120px] p-2">
                <Pencil className="w-[17px] h-[17px]" /> Update Feature
            </Button>
            <SearchableDropdown title="Login Functionality" createdOn="5/15/2024" items={featureList} />
            <div className="flex flex-col rounded-lg mt-[16px] border border-[#F0F0F0] px-[18px] py-[16px] bg-white">
                <div className="ml-[15px] flex flex-row items-center justify-between">
                    <div className="flex flex-row items-center gap-2">
                        <div className="flex justify-center items-center rounded-full w-[25px] h-[25px] px-[6px] py-[7px] bg-[#8181821F]">
                            <Image src={'/assets/icons/tasks.svg'} width={14} height={11} alt="Tasks Icon" />
                        </div>
                        <h1 className="pl-[7px] text-[17px] font-semibold text-[#363636]">Total Tasks</h1>
                        <div className="h-[20px] w-[30px] flex justify-center items-center p-[5px] rounded-[40px] bg-[#dfdfdfde]">
                            <span className="text-[13px] font-semibold text-[#363636]">12</span>
                        </div>
                    </div>
                    <div className="cursor-pointer" onClick={() => setExpanded(!expanded)}>
                        <Image src={'/assets/icons/fullscreen.svg'} height={24} width={24} alt="Fullscreen Icon" />
                    </div>
                </div>
                <ScrollArea className="mt-[10px] h-[65vh] p-4 overflow-visible">
                    {dummyTaskData.length ? (
                        dummyTaskData.map((task, i) => (
                            <TaskCard
                                taskNum={i + 1}
                                key={task.id}
                                id={task.id}
                                category={task.category}
                                title={task.title}
                                description={task.description}
                                assignedRole={task.assignedRole}
                                estimatedHours={task.estimatedHours}
                                dependencies={task.dependencies}
                            />
                        ))
                    ) : (
                        <p>Nothing to display. enter a requirement</p>
                    )}
                </ScrollArea>
            </div>
        </div>
    )
}
