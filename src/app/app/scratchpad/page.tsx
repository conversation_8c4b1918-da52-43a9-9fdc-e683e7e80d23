'use client'
import { useEffect } from 'react'

import ScratchpadTutorial from '@/components/scratchpad-tutorial'
import { Button } from '@/components/ui/button'
import { useRouter } from 'next/navigation'
import { useBreadcrumbStore } from '@/store/breadcrumb.store'
import { useProjectStore } from '@/store/scratchpad-projects.store'

export default function Page() {
    const router = useRouter()

    const { setBreadcrumbs } = useBreadcrumbStore()
    const { setSelectedProject } = useProjectStore()

    // Simulating an API response
    const projectsData = [
        { id: '1', name: 'AI Research' },
        { id: '2', name: 'Web Development' },
        { id: '3', name: 'Mobile App' },
    ]

    // Function to set the selected project
    const handleProjectSelect = (projectId: string) => {
        const selectedProject = projectsData.find((project) => project.id === projectId)
        if (selectedProject) {
            setSelectedProject(selectedProject)
            router.push(`scratchpad/${projectId}`)
        }
    }

    const handleClick = () => {}

    useEffect(() => {
        setBreadcrumbs([
            { label: 'Dashboard', href: '/' },
            { label: 'ScratchPad', href: '/app/scratchpad' },
        ])
    }, [setBreadcrumbs])

    return (
        <div>
            <ScratchpadTutorial onClick={handleClick} />
            <div className="flex flex-col gap-4">
                {projectsData.map((project) => (
                    <Button key={project.id} onClick={() => handleProjectSelect(project.id)} className="w-fit">
                        {project.name}
                    </Button>
                ))}
            </div>
        </div>
    )
}
