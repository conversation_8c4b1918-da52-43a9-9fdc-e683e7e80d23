'use client'

import { useEffect, useState } from 'react'
import { usePara<PERSON>, useRouter } from 'next/navigation'
import { Button } from '@/components/ui/button'
import { api } from '@/config/axios-config'
import endpoints from '@/services/api-endpoints'
import { useAuthStore } from '@/store/auth.store'
import { toast } from 'sonner'
import { AxiosError } from 'axios'
import Image from 'next/image'
import { AssigneeAvatar } from '@/components/assignee-avatar-with-fallback'
import { Separator } from '@/components/ui/separator'
import ExpiredInvite from './expired-invite'

type InviteStatus = 'loading' | 'not-logged-in' | 'email-mismatch' | 'match' | 'error'

export default function JoinWorkspacePage() {
    const { token } = useParams<{ token: string }>()
    const router = useRouter()
    const [status, setStatus] = useState<InviteStatus>('loading')
    const [workspaceDeatils, setWorkspaceDetails] = useState({ name: '', img_url: '' })
    const [inviteEmail, setInviteEmail] = useState<string | null>(null)
    const [error, setError] = useState('')
    // const [expiredWorkspace, setExpiredWorkspace] = useState({ name: '', img_url: '' })
    const logout = useAuthStore((state) => state.logout)

    useEffect(() => {
        const fetchInvite = async () => {
            try {
                const res = await api.get(endpoints.workspace.join + `/${token}`)

                setWorkspaceDetails(res.data.data.workspace)
                setInviteEmail(res.data.data.email)
                setStatus(res.data.data.matchStatus)
            } catch (err: unknown) {
                if (err instanceof AxiosError) {
                    setError(err.response?.data?.message || 'Invalid invite')
                    // setExpiredWorkspace(err.response?.data?.scopeErrors?.workspace || { name: '', img_url: '' })
                } else {
                    setError('Invalid invite')
                }
                setStatus('error')
            }
        }

        fetchInvite()
    }, [token])
    const handleLogout = async () => {
        try {
            await logout()
            router.push(`/sign-in/?redirect=/join/${token}`)
        } catch {
            toast.error('Failed to logout. Please try again.')
        }
    }
    const handleJoin = async () => {
        try {
            await api.post(endpoints.workspace.join + `/${token}`)
            router.push('/app/chat') // or wherever you want after joining
        } catch (err: unknown) {
            if (err instanceof AxiosError) {
                toast.error(err.response?.data?.message || 'Could not join workspace')
                setError(err.response?.data?.message || 'Could not join workspace')
            }
        }
    }

    if (status === 'loading') return <p className="p-4">Loading...</p>
    if (status === 'error') return <ExpiredInvite />

    return (
        <div className="max-w-md shadow-lg overflow-hidden rounded-[12px]">
            <Image src="/assets/img/team-image.png" width={462} height={173} alt="Logo" className="w-full h-auto object-cover" />
            <div className="p-6 w-full mx-auto text-center space-y-4">
                <div className="flex justify-center items-center">
                    <AssigneeAvatar
                        assignee={workspaceDeatils.name}
                        imageUrl={workspaceDeatils.img_url}
                        className=" h-[48px] w-[48px] rounded-md"
                        fallbackClassName="bg-[#149469] rounded-md text-white"
                    />
                </div>
                <p className="text-lg font-semibold">Join {workspaceDeatils.name}</p>
                {inviteEmail && (
                    <p className="text-xs mx-auto text-muted-foreground w-1/2">You&apos;ve been invited via {inviteEmail}</p>
                )}
                <Separator />

                {status === 'not-logged-in' && (
                    <div className="space-y-2 w-full">
                        <Button
                            className="w-full text-[15px] font-medium"
                            onClick={() => router.push(`/sign-in/?redirect=/join/${token}`)}>
                            Login
                        </Button>
                        <Button
                            className="w-full text-[15px] font-medium"
                            onClick={() => router.push(`/sign-up/?redirect=/join/${token}`)}>
                            SignUp
                        </Button>
                        {/* <p className="text-red-500">Please log in to accept the invitation.</p> */}
                    </div>
                )}

                {status === 'email-mismatch' && (
                    <>
                        <p className="text-red-500">This invite is for {inviteEmail}. Please switch accounts.</p>
                        <Button className="text-[15px] font-medium" onClick={handleLogout}>
                            Logout
                        </Button>
                    </>
                )}

                {status === 'match' && (
                    <Button className="text-[15px] font-medium" onClick={handleJoin}>
                        Join Workspace
                    </Button>
                )}

                {error && <p className="text-red-600">{error}</p>}
            </div>
        </div>
    )
}
