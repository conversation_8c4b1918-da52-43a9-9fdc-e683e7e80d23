import type { Metadata } from 'next'
import './globals.css'
import { Toaster } from '@/components/ui/sonner'
import QueryProvider from '@/components/query-provider'
import { Inter } from 'next/font/google'

export const metadata: Metadata = {
    title: 'Raydian.ai',
    description: 'Accelerate your project development',
    icons: {
        icon: '/favicon_io/favicon.ico',
    },
}

const inter = Inter({
    subsets: ['latin'],
    display: 'swap',
    variable: '--font-inter',
})

export default function RootLayout({ children }: { children: React.ReactNode }) {
    return (
        <html lang="en">
            <body className={`${inter.className} antialiased`}>
                <QueryProvider>{children}</QueryProvider>
                <Toaster />
            </body>
        </html>
    )
}
