@import url('https://fonts.googleapis.com/css2?family=Inter:wght@100;200;300;400;500;600;700;800;900&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Playfair+Display:wght@400;500;600;700;800;900&display=swap');

@import 'tailwindcss';

@plugin "tailwindcss-animate";

@custom-variant dark (&:is(.dark *));

@theme {
    --font-sans: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
    --font-serif: 'Playfair Display', serif;
    --font-mono: var(--font-geist-mono);
    --breakpoint-3xl: 120rem;
}

.custom-scroll::-webkit-scrollbar {
    width: 4px;
}

.custom-scroll::-webkit-scrollbar-thumb {
    background-color: #374151;
    border-radius: 10px;
}

.custom-scroll::-webkit-scrollbar-track {
    background-color: #37415112;
}

:root {
    /* --background: oklch(1 0 0); */
    --background: #fafafa;
    --foreground: oklch(0.13 0.028 261.692);
    --card: oklch(1 0 0);
    --card-foreground: oklch(0.13 0.028 261.692);
    --popover: oklch(1 0 0);
    --popover-foreground: oklch(0.13 0.028 261.692);
    --primary: oklch(0.21 0.034 264.665);
    --primary-foreground: oklch(0.985 0.002 247.839);
    --secondary: oklch(0.967 0.003 264.542);
    --secondary-foreground: oklch(0.21 0.034 264.665);
    --muted: oklch(0.967 0.003 264.542);
    --muted-foreground: oklch(0.551 0.027 264.364);
    --accent: oklch(0.967 0.003 264.542);
    --accent-foreground: oklch(0.21 0.034 264.665);
    --destructive: oklch(0.577 0.245 27.325);
    --destructive-foreground: oklch(0.577 0.245 27.325);
    --border: oklch(0.928 0.006 264.531);
    --input: oklch(0.928 0.006 264.531);
    --ring: oklch(0.707 0.022 261.325);
    --chart-1: oklch(0.646 0.222 41.116);
    --chart-2: oklch(0.6 0.118 184.704);
    --chart-3: oklch(0.398 0.07 227.392);
    --chart-4: oklch(0.828 0.189 84.429);
    --chart-5: oklch(0.769 0.188 70.08);
    --radius: 0.625rem;
    --sidebar: oklch(0.985 0.002 247.839);
    --sidebar-foreground: oklch(0.13 0.028 261.692);
    --sidebar-primary: oklch(0.21 0.034 264.665);
    --sidebar-primary-foreground: oklch(0.985 0.002 247.839);
    --sidebar-accent: oklch(0.967 0.003 264.542);
    --sidebar-accent-foreground: oklch(0.21 0.034 264.665);
    --sidebar-border: oklch(0.928 0.006 264.531);
    --sidebar-ring: oklch(0.707 0.022 261.325);
}

.dark {
    --background: oklch(0.13 0.028 261.692);
    --foreground: oklch(0.985 0.002 247.839);
    --card: oklch(0.13 0.028 261.692);
    --card-foreground: oklch(0.985 0.002 247.839);
    --popover: oklch(0.13 0.028 261.692);
    --popover-foreground: oklch(0.985 0.002 247.839);
    --primary: oklch(0.985 0.002 247.839);
    --primary-foreground: oklch(0.21 0.034 264.665);
    --secondary: oklch(0.278 0.033 256.848);
    --secondary-foreground: oklch(0.985 0.002 247.839);
    --muted: oklch(0.278 0.033 256.848);
    --muted-foreground: oklch(0.707 0.022 261.325);
    --accent: oklch(0.278 0.033 256.848);
    --accent-foreground: oklch(0.985 0.002 247.839);
    --destructive: oklch(0.396 0.141 25.723);
    --destructive-foreground: oklch(0.637 0.237 25.331);
    --border: oklch(0.278 0.033 256.848);
    --input: oklch(0.278 0.033 256.848);
    --ring: oklch(0.446 0.03 256.802);
    --chart-1: oklch(0.488 0.243 264.376);
    --chart-2: oklch(0.696 0.17 162.48);
    --chart-3: oklch(0.769 0.188 70.08);
    --chart-4: oklch(0.627 0.265 303.9);
    --chart-5: oklch(0.645 0.246 16.439);
    --sidebar: oklch(0.21 0.034 264.665);
    --sidebar-foreground: oklch(0.985 0.002 247.839);
    --sidebar-primary: oklch(0.488 0.243 264.376);
    --sidebar-primary-foreground: oklch(0.985 0.002 247.839);
    --sidebar-accent: oklch(0.278 0.033 256.848);
    --sidebar-accent-foreground: oklch(0.985 0.002 247.839);
    --sidebar-border: oklch(0.278 0.033 256.848);
    --sidebar-ring: oklch(0.446 0.03 256.802);
}

@theme inline {
    --color-background: var(--background);
    --color-foreground: var(--foreground);
    --color-card: var(--card);
    --color-card-foreground: var(--card-foreground);
    --color-popover: var(--popover);
    --color-popover-foreground: var(--popover-foreground);
    --color-primary: var(--primary);
    --color-primary-foreground: var(--primary-foreground);
    --color-secondary: var(--secondary);
    --color-secondary-foreground: var(--secondary-foreground);
    --color-muted: var(--muted);
    --color-muted-foreground: var(--muted-foreground);
    --color-accent: var(--accent);
    --color-accent-foreground: var(--accent-foreground);
    --color-destructive: var(--destructive);
    --color-destructive-foreground: var(--destructive-foreground);
    --color-border: var(--border);
    --color-input: var(--input);
    --color-ring: var(--ring);
    --color-chart-1: var(--chart-1);
    --color-chart-2: var(--chart-2);
    --color-chart-3: var(--chart-3);
    --color-chart-4: var(--chart-4);
    --color-chart-5: var(--chart-5);
    --radius-sm: calc(var(--radius) - 4px);
    --radius-md: calc(var(--radius) - 2px);
    --radius-lg: var(--radius);
    --radius-xl: calc(var(--radius) + 4px);
    --color-sidebar: var(--sidebar);
    --color-sidebar-foreground: var(--sidebar-foreground);
    --color-sidebar-primary: var(--sidebar-primary);
    --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
    --color-sidebar-accent: var(--sidebar-accent);
    --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
    --color-sidebar-border: var(--sidebar-border);
    --color-sidebar-ring: var(--sidebar-ring);
}

@layer base {
    html {
        font-family: 'Inter', sans-serif;
    }

    * {
        @apply border-border outline-ring/50;
    }

    body {
        @apply bg-background text-foreground;
    }
}
