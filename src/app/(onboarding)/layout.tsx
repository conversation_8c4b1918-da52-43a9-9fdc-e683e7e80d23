import Link from 'next/link'
import OnboardingRightSection from './onboarding-right-section'
import Image from 'next/image'

export default function Layout({ children }: { children: React.ReactNode }) {
    return (
        <div className="flex flex-row w-full h-screen overflow-hidden">
            <div className="w-full md:w-[60%] flex justify-center items-center bg-[#fff]">
                <Link
                    href="/"
                    className="flex items-center gap-2 hover:opacity-80 transition-opacity absolute top-20 left-10 md:top-8 sm:top-0 md:left-61 sm:left-20  z-10">
                    <Image src="/assets/img/raydian-logo.png" alt="Logo" width={28} height={28} className="h-auto w-auto" />
                    <span className="font-semibold text-lg">Raydian</span>
                </Link>

                <div className="w-full">{children}</div>
            </div>
            <div className="hidden md:block w-[40%]">
                <OnboardingRightSection />
            </div>
        </div>
    )
}
