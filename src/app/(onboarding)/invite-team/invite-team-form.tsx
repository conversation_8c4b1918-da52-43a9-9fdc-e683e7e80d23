'use client'

import { Suspense, useEffect, useState } from 'react'
import { useForm, useFieldArray } from 'react-hook-form'
import { z } from 'zod'
import { zodResolver } from '@hookform/resolvers/zod'

import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Card, CardContent } from '@/components/ui/card'
import { CircleX, Plus } from 'lucide-react'

import { Form, FormControl, FormField, FormItem, FormMessage } from '@/components/ui/form'
import { findDuplicates, hasEmptyEmail } from '@/utils/email-utils'
import Stepper from '@/components/steppers'
import FormOverlayWrapper from '@/components/form-overlay-wrapper'
import { useMutation } from '@tanstack/react-query'
import { api } from '@/config/axios-config'
import { toast } from 'sonner'
import endpoints from '@/services/api-endpoints'
import { useRouter, useSearchParams } from 'next/navigation'
import { useAuthStore } from '@/store/auth.store'
import { AxiosError } from 'axios'
import { axiosErrorToast } from '@/utils/axios-error-toast.utils'

// Schema with duplicate check
const schema = z.object({
    emails: z
        .array(
            z.object({
                value: z.string().min(1, 'Email is required').email('Invalid email address'),
            }),
        )
        .min(1, 'At least one email is required'),
})

type FormSchema = z.infer<typeof schema>

export default function InviteTeam() {
    const [linkCopied, setLinkCopied] = useState(false)
    const [inviteLink, setInviteLink] = useState('')
    const router = useRouter()
    const searchParams = useSearchParams()
    const workspace_id = searchParams.get('workspaceId')

    const inviteMutation = useMutation({
        mutationFn: async (data: { emails?: string[]; link?: string; action: string }) => {
            const url = endpoints.workspace.invite.replace(':id', workspace_id || '')

            const response = await api.post(url, data)
            if (response.data.success && data.action === 'email') {
                document.cookie = 'auth_session=true; path=/; max-age=86400; samesite=strict; secure'
                setPendingVerificationEmail(null)
                router.push('/app')
            }
            return response.data
        },
        onSuccess: (data) => {
            setInviteLink(data.invite_link)
        },
        onError: (error: AxiosError) => {
            const errorText = (error.response?.data as { error?: string })?.error
            axiosErrorToast(error, errorText || 'Failed to send invite. Please try again.')
        },
    })

    useEffect(() => {
        if (!workspace_id) return
        inviteMutation.mutate({ action: 'copy' })
        //eslint-disable-next-line react-hooks/exhaustive-deps
    }, [])

    const setPendingVerificationEmail = useAuthStore((state) => state.setPendingVerificationEmail)

    const form = useForm<FormSchema>({
        resolver: zodResolver(schema),
        defaultValues: {
            emails: [{ value: '' }],
        },
    })

    const { control, handleSubmit } = form

    const { fields, append, remove } = useFieldArray({
        control,
        name: 'emails',
    })
    const SHAREABLE_LINK = inviteLink || 'http://example.com/link/to/document'

    const duplicateEmail = findDuplicates(form.watch('emails')) || ''
    const hasEmptyEmailField = hasEmptyEmail(form.watch('emails'))

    const onSubmit = (data: FormSchema) => {
        const toastId = toast.loading('Sending invite...') // Show loading toast
        inviteMutation.mutate(
            { emails: data.emails.map((email) => email.value.trim()), action: 'email' },
            {
                onSettled: () => {
                    toast.dismiss(toastId) // Dismiss the loading toast when the mutation is settled
                },
            },
        )
    }

    const copyLink = () => {
        navigator.clipboard.writeText(SHAREABLE_LINK)
        setLinkCopied(true)
        setTimeout(() => setLinkCopied(false), 3000)
    }

    const handleSkip = () => {
        document.cookie = 'auth_session=true; path=/; max-age=86400; samesite=strict; secure'
        router.push('/app')
        setPendingVerificationEmail(null)
    }

    return (
        <Suspense>
            <FormOverlayWrapper isSubmitting={inviteMutation.isPending}>
                <Card className="mx-auto max-w-xl  bg-white border-none shadow-none">
                    <CardContent className="pt-6">
                        <div className="my-12">
                            <Stepper totalSteps={3} currentStep={2} />
                        </div>

                        <h1 className="text-2xl font-bold mb-1">Invite Team</h1>
                        <p className="text-gray-500 text-sm mb-6">
                            Start your Anyone with the link can view this document. 30-day free trial.
                        </p>

                        <Form {...form}>
                            <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
                                <div className="flex space-x-2">
                                    <Input value={SHAREABLE_LINK} readOnly className="bg-gray-50 rounded-sm" />
                                    <Button
                                        variant="outline"
                                        onClick={copyLink}
                                        type="button"
                                        className="whitespace-nowrap rounded-sm">
                                        {linkCopied ? 'Copied!' : 'Copy Link'}
                                    </Button>
                                </div>

                                <div className="flex items-center justify-center text-gray-500 text-sm">
                                    <div className="border-t flex-grow"></div>
                                    <span className="px-3">or</span>
                                    <div className="border-t flex-grow"></div>
                                </div>

                                <div>
                                    <h3 className="text-sm font-medium mb-2">Invite with Email</h3>
                                    <div className="space-y-2">
                                        {fields.map((field, index) => (
                                            <FormField
                                                key={field.id}
                                                control={control}
                                                name={`emails.${index}.value`}
                                                render={({ field }) => (
                                                    <FormItem>
                                                        <div className="relative">
                                                            <FormControl>
                                                                <Input
                                                                    // type="email"
                                                                    placeholder="Email address"
                                                                    {...field}
                                                                    className="bg-white rounded-sm"
                                                                />
                                                            </FormControl>
                                                            <Button
                                                                variant="ghost"
                                                                className="absolute right-0 top-2 h-5 w-5 rounded-full"
                                                                onClick={() => remove(index)}
                                                                type="button"
                                                                // disabled={index === 0}
                                                            >
                                                                <CircleX />
                                                            </Button>
                                                        </div>
                                                        <FormMessage />
                                                    </FormItem>
                                                )}
                                            />
                                        ))}
                                        {/* Show general error for duplicates */}
                                        {duplicateEmail.length > 0 && (
                                            <p className="text-sm text-red-500 mt-1">
                                                {duplicateEmail.join(', ')} is repeated. Please remove duplicates.
                                            </p>
                                        )}
                                        {form.formState.errors.emails && (
                                            <p className="text-sm text-red-500 mt-1">
                                                {form.formState.errors.emails.message ||
                                                    form.formState.errors.emails.root?.message}
                                            </p>
                                        )}
                                        <Button
                                            type="button"
                                            variant="ghost"
                                            className="flex items-center text-sm mt-2"
                                            disabled={
                                                fields.length > 0 &&
                                                (duplicateEmail.length > 0 ||
                                                    form.formState.errors.emails !== undefined ||
                                                    hasEmptyEmailField)
                                            }
                                            onClick={() => {
                                                append({ value: '' })
                                                form.clearErrors()
                                            }}>
                                            <Plus className="w-4 h-4 mr-1" />
                                            Add another
                                        </Button>
                                    </div>
                                </div>

                                <div className="flex space-x-3">
                                    <Button
                                        onClick={handleSkip}
                                        variant="outline"
                                        type="button"
                                        className="flex-1 bg-gray-100 hover:bg-gray-200 border-gray-200 rounded-sm">
                                        Skip for now
                                    </Button>
                                    <Button type="submit" className="flex-1 bg-black hover:bg-gray-800 text-white rounded-sm">
                                        Invite
                                    </Button>
                                </div>
                            </form>
                        </Form>
                    </CardContent>
                </Card>
            </FormOverlayWrapper>
        </Suspense>
    )
}
