'use client'

import { useEffect, useState } from 'react'
import { useForm } from 'react-hook-form'
import { z } from 'zod'
import { zodResolver } from '@hookform/resolvers/zod'

import { Button } from '@/components/ui/button'
import { Card, CardContent } from '@/components/ui/card'
import { Loader, Upload } from 'lucide-react'
import FormInputField from '@/components/form-fields/form-input-field'
import { Form, FormField } from '@/components/ui/form'
import { FormSelectField } from '@/components/form-fields'
import Image from 'next/image'
import Stepper from '@/components/steppers'
import { useMutation } from '@tanstack/react-query'
import { api } from '@/config/axios-config'
import { toast } from 'sonner'
import endpoints from '@/services/api-endpoints'
import FormOverlayWrapper from '@/components/form-overlay-wrapper'
import { useRouter } from 'next/navigation'
import { uploadFile } from '@/lib/minio-client'
import { useAuthStore } from '@/store/auth.store'
import workIcon from '../../../../public/assets/icons/workIcon.svg'

const formSchema = z.object({
    workspaceName: z.string().min(1, 'Workspace Name is required'),
    companySize: z.string().min(1, 'Company size is required'),
    role: z.string().min(1, 'Role is required'),
})

export default function CreateWorkspace() {
    const [logoImage, setLogoImage] = useState<File | null>(null)
    const router = useRouter()
    const [hasChecked, setHasChecked] = useState(false)
    const pendingVerificationEmail = useAuthStore((state) => state.pendingVerificationEmail)

    useEffect(() => {
        if (!hasChecked) {
            // Skip first render where value might be null due to hydration
            setHasChecked(true)
            return
        }
        if (!pendingVerificationEmail) {
            router.replace('/sign-up')
            return
        }
    }, [pendingVerificationEmail, router, hasChecked])

    const companySizes = [
        { value: '1-10', label: '1-10 employees' },
        { value: '11-50', label: '11-50 employees' },
        { value: '51-200', label: '51-200 employees' },
        { value: '201-500', label: '201-500 employees' },
        { value: '501+', label: '501+ employees' },
    ]

    const companyRoles = [
        { value: 'founder', label: 'Founder / CEO' },
        { value: 'executive', label: 'Executive / C-level' },
        { value: 'manager', label: 'Manager / Team Lead' },
        { value: 'developer', label: 'Developer / Engineer' },
        { value: 'designer', label: 'Designer' },
        { value: 'marketing', label: 'Marketing / Sales' },
        { value: 'other', label: 'Other' },
    ]

    const form = useForm<z.infer<typeof formSchema>>({
        resolver: zodResolver(formSchema),
        defaultValues: {
            workspaceName: '',
            companySize: '',
            role: '',
        },
    })

    const handleImageUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
        if (e.target.files && e.target.files[0]) {
            setLogoImage(e.target.files[0])
        }
    }

    const removeImage = () => {
        setLogoImage(null)
    }
    interface FileUpload {
        success: boolean
        fileUrl?: string | null
        error?: string
    }
    const mutation = useMutation({
        mutationFn: async (data: z.infer<typeof formSchema>) => {
            let result: FileUpload = { success: false, fileUrl: '' } // Initialize result
            let body: { name: string; plan: number; img_url?: string } = {
                name: data.workspaceName,
                plan: 0,
            }
            if (logoImage) {
                const formData = new FormData()
                formData.append('file', logoImage)
                result = await uploadFile(formData)
            }

            if (logoImage && !result.success) {
                throw new Error('Upload failed')
            }
            if (result.fileUrl) {
                body = { ...body, img_url: result.fileUrl }
            }

            const response = await api.post(endpoints.onboarding.createWorkSpace, body)
            return response.data
        },
        onSuccess: (data) => {
            toast.success('Workspace created successfully!')
            const worksapceid = data.data.id
            router.push('invite-team?workspaceId=' + worksapceid)
        },
        onError: (error: Error) => {
            toast.error(error.message || 'Failed to create workspace. Please try again.')
        },
    })

    const onSubmit = (values: z.infer<typeof formSchema>) => {
        const toastId = toast.loading('Creating work space...') // Show loading toast
        mutation.mutate(values, {
            onSettled: () => {
                toast.dismiss(toastId) // Dismiss the loading toast when the mutation is settled
            },
        })
    }

    if (!pendingVerificationEmail) return null
    return (
        <FormOverlayWrapper isSubmitting={mutation.isPending}>
            <Card className="mx-auto px-10 max-w-xl bg-white border-none shadow-none">
                <CardContent className="pt-6">
                    <Stepper totalSteps={3} currentStep={1} />

                    <h1 className="text-2xl font-bold mb-1">Create Workspace</h1>
                    <p className="text-gray-500 text-sm mb-6">Start your 30-day free trial.</p>
                    <Form {...form}>
                        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
                            {/* Workspace Logo */}
                            <div>
                                <div className="flex items-center">
                                    <div className="relative mr-2 mt-2">
                                        <div className="w-12 h-12 border justify-center rounded-full flex items-center bg-gray-50">
                                            {logoImage ? (
                                                <Image
                                                    src={URL.createObjectURL(logoImage)}
                                                    alt="Workspace Logo"
                                                    className="w-10 h-10 rounded-full object-cover"
                                                    fill
                                                />
                                            ) : (
                                                <Image
                                                    src={workIcon}
                                                    alt="Workspace Logo"
                                                    width={26}
                                                    height={26}
                                                    className="rounded-full object-cover"
                                                />
                                            )}
                                        </div>
                                    </div>

                                    <div>
                                        <label className="block text-sm font-medium mb-2">Workspace Logo</label>
                                        <div className="flex space-x-2">
                                            <Button variant="outline" size="sm" className="text-xs" asChild>
                                                <label>
                                                    Upload Image
                                                    <input
                                                        type="file"
                                                        className="hidden"
                                                        accept="image/*"
                                                        onChange={handleImageUpload}
                                                    />
                                                    <Upload className="ml-1 h-3 w-3" />
                                                </label>
                                            </Button>
                                            <Button
                                                variant="ghost"
                                                size="sm"
                                                onClick={removeImage}
                                                className="text-xs"
                                                disabled={!logoImage}>
                                                Remove
                                            </Button>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            {/* Workspace Name */}

                            <FormField
                                control={form.control}
                                name="workspaceName"
                                render={({ field }) => (
                                    <FormInputField label="Workspace Name" placeholder="Your workspace name" field={field} />
                                )}
                            />

                            {/* Company Size */}
                            <FormField
                                control={form.control}
                                name="companySize"
                                render={({ field }) => (
                                    <FormSelectField
                                        label="How Large is your Company"
                                        placeholder="Select a verified email to display"
                                        field={field}
                                        options={companySizes}
                                    />
                                )}
                            />

                            {/* Role */}
                            <FormField
                                control={form.control}
                                name="role"
                                render={({ field }) => (
                                    <FormSelectField
                                        label="What is your role?"
                                        placeholder="Select your role in the company"
                                        field={field}
                                        options={companyRoles}
                                    />
                                )}
                            />
                            {/* Submit Button */}
                            <Button type="submit" className="w-full bg-black hover:bg-gray-800 text-white">
                                {mutation.isPending ? <Loader className="animate-spin" /> : 'Create Workspace'}
                            </Button>
                        </form>
                    </Form>
                </CardContent>
            </Card>
        </FormOverlayWrapper>
    )
}
